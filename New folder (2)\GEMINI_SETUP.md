# 🤖 Google Gemini AI Integration Setup

This guide explains how to integrate Google Gemini AI with your GreenMate application for intelligent eco-friendly advice.

## 🔑 Getting Your Gemini API Key

### Step 1: Access Google AI Studio
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Accept the terms of service if prompted

### Step 2: Create API Key
1. Click **"Create API Key"** button
2. Choose **"Create API key in new project"** (recommended)
3. Copy the generated API key (starts with `AIza...`)
4. **Important**: Keep this key secure and never commit it to version control

### Step 3: Configure Environment
1. Copy the example environment file:
   ```bash
   cp server/.env.example server/.env
   ```

2. Edit `server/.env` and add your API key:
   ```env
   GOOGLE_GEMINI_API_KEY=AIzaSyC-your-actual-api-key-here
   ```

## 📦 Installation

The Gemini package is already installed, but if you need to install it manually:

```bash
cd server
npm install @google/generative-ai
```

## 🚀 Features Enabled

With Gemini AI integration, your app gets:

### ✅ **Intelligent Chat Assistant**
- Personalized eco-friendly advice
- Context-aware responses based on user level and habits
- Follow-up suggestions for deeper learning
- Action items for immediate implementation

### ✅ **Smart Habit Recommendations**
- AI-powered habit suggestions based on user profile
- Difficulty-appropriate recommendations
- Seasonal and location-aware advice

### ✅ **Environmental Impact Analysis**
- Intelligent analysis of user's eco-friendly actions
- Personalized tips for improvement
- Goal-setting assistance

## 🔧 How It Works

### 1. **Fallback System**
The app works with or without the API key:
- **With API key**: Uses Google Gemini for intelligent responses
- **Without API key**: Falls back to curated sample responses

### 2. **Context-Aware Responses**
The AI considers:
- User's current eco-level
- Total points earned
- Active habits
- Previous interactions

### 3. **Smart Prompting**
Each request includes:
- User context for personalization
- Specific instructions for eco-friendly focus
- Response format guidelines
- Length and tone requirements

## 🛡️ Security & Best Practices

### ✅ **API Key Security**
- Never commit API keys to version control
- Use environment variables only
- Rotate keys periodically
- Monitor usage in Google Cloud Console

### ✅ **Rate Limiting**
- Gemini has generous free tier limits
- Monitor usage to avoid unexpected charges
- Implement client-side debouncing for chat

### ✅ **Error Handling**
- Graceful fallback to sample responses
- User-friendly error messages
- Logging for debugging

## 💰 Pricing Information

### **Free Tier** (Generous limits)
- 15 requests per minute
- 1,500 requests per day
- 1 million tokens per month

### **Paid Tier** (If needed)
- $0.00025 per 1K characters (input)
- $0.0005 per 1K characters (output)
- Very cost-effective for most applications

## 🧪 Testing the Integration

### 1. **Start the Application**
```bash
npm run dev
```

### 2. **Test AI Assistant**
1. Navigate to the AI Assistant page
2. Try asking: "Suggest eco-friendly habits for beginners"
3. Check for intelligent, personalized responses

### 3. **Verify Fallback**
1. Remove or comment out the API key in `.env`
2. Restart the server
3. Confirm sample responses still work

## 🔍 Troubleshooting

### **Common Issues:**

#### ❌ "API key not configured" error
- **Solution**: Check `.env` file has `GOOGLE_GEMINI_API_KEY=your_key`
- **Solution**: Restart the server after adding the key

#### ❌ "Invalid API key" error
- **Solution**: Verify the API key is correct and active
- **Solution**: Check Google AI Studio for key status

#### ❌ "Rate limit exceeded" error
- **Solution**: Wait for rate limit reset (1 minute)
- **Solution**: Implement request queuing if needed

#### ❌ Responses seem generic
- **Solution**: Check user context is being passed correctly
- **Solution**: Verify user profile data exists

## 🚀 Advanced Configuration

### **Custom Model Settings**
You can modify the model configuration in `server/services/geminiService.js`:

```javascript
// Use different model
const model = genAI.getGenerativeModel({ 
  model: "gemini-pro-vision" // For image analysis
});

// Add safety settings
const model = genAI.getGenerativeModel({
  model: "gemini-pro",
  safetySettings: [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    }
  ]
});
```

### **Enhanced Prompting**
Customize prompts for specific use cases:
- Seasonal advice
- Location-based recommendations
- User skill level adaptation
- Cultural considerations

## 📊 Monitoring Usage

### **Google Cloud Console**
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to "APIs & Services" > "Credentials"
3. Monitor API key usage and quotas

### **Application Logs**
Check server logs for:
- API call success/failure rates
- Response times
- Error patterns
- User interaction patterns

## 🎯 Next Steps

1. **Get your API key** from Google AI Studio
2. **Add it to your `.env` file**
3. **Restart the server**
4. **Test the AI assistant**
5. **Enjoy intelligent eco-friendly advice!**

---

**Need Help?** 
- Check the [Google AI Studio documentation](https://ai.google.dev/docs)
- Review the implementation in `server/services/geminiService.js`
- Test with sample responses first, then add the API key
