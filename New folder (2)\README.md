# 🌱 GreenMate - Your Personal Eco-Friendly Planner

A comprehensive web application that helps users track their daily eco-friendly habits, earn eco points, and build a sustainable lifestyle through gamification and community engagement.

## ✨ Features

### 🎯 Core Features
- ✅ **Daily Habit Tracker** - Customizable eco-friendly habits
- 📈 **Impact Dashboard** - Track CO₂ saved, water conserved, electricity saved
- 🏅 **Eco Points System** - Earn rewards for consistency
- 🎁 **Rewards System** - Digital badges and discounts
- 🌍 **Daily Eco Tips** - Personalized sustainability advice
- 👥 **Community Leaderboard** - Compete with other eco-warriors

### 🚀 Advanced Features
- 📍 **Eco-Friendly Places Map** - Find nearby recycling centers, organic shops, EV stations
- 🧠 **AI Eco Assistant** - Get smart eco-friendly tips using Gemini API
- 🗓️ **Habit Calendar** - Visual streak tracking like Duolingo
- 🏆 **Community Challenges** - Join challenges like "Plastic-Free Week"
- 🌐 **Multi-language Support** - English, Tamil, and Tanglish
- 🧑‍🤝‍🧑 **Social Features** - Follow friends and share achievements

## 🛠️ Tech Stack

- **Frontend**: React + Vite, Tailwind CSS
- **Backend**: Node.js + Express.js
- **Database**: MongoDB with Mongoose
- **Maps**: Leaflet.js with OpenStreetMap
- **AI**: Gemini API integration
- **Authentication**: JWT tokens
- **State Management**: React Context API

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. **Clone and install dependencies**
```bash
git clone <repository-url>
cd greenmate
npm run install-all
```

2. **Set up environment variables**
```bash
# In server/.env
MONGODB_URI=mongodb://localhost:27017/greenmate
JWT_SECRET=your_jwt_secret_here
GEMINI_API_KEY=your_gemini_api_key_here
PORT=5000

# In client/.env
VITE_API_URL=http://localhost:5000/api
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

3. **Start development servers**
```bash
npm run dev
```

This will start:
- Backend server on http://localhost:5000
- Frontend development server on http://localhost:5173

## 📱 Usage

1. **Sign up/Login** - Create your eco-warrior account
2. **Set Daily Habits** - Choose from preset habits or create custom ones
3. **Track Progress** - Mark completed habits daily
4. **Earn Points** - Get eco points for consistency
5. **View Impact** - See your environmental impact stats
6. **Join Community** - Participate in challenges and compete on leaderboards
7. **Get Tips** - Receive personalized eco-friendly advice
8. **Find Places** - Discover eco-friendly locations near you

## 🌍 Environmental Impact

GreenMate helps users track their positive environmental impact:
- **CO₂ Reduction**: Track carbon footprint savings
- **Water Conservation**: Monitor water usage reduction
- **Energy Savings**: Calculate electricity conservation
- **Waste Reduction**: Track plastic and waste reduction

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for more details.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenStreetMap for map data
- Google Gemini for AI assistance
- The open-source community for amazing tools and libraries

---

**Made with 💚 for a sustainable future**
