import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Create simplified versions of contexts to avoid import issues
const AuthContext = React.createContext();
const AuthProvider = ({ children }) => {
  const [user, setUser] = React.useState(null);
  const [loading, setLoading] = React.useState(false);

  return (
    <AuthContext.Provider value={{ user, setUser, loading, setLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Simple Home component with eco-friendly styling
const Home = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
    {/* Hero Section */}
    <div className="relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl md:text-7xl font-bold text-gray-900 mb-6">
            Welcome to{' '}
            <span className="bg-gradient-eco bg-clip-text text-transparent">
              GreenMate
            </span>
            🌱
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Your AI-powered companion for sustainable living. Track habits, reduce your carbon footprint, and make a positive impact on our planet.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-eco px-8 py-4 text-lg font-semibold rounded-xl">
              Get Started 🚀
            </button>
            <button className="btn-secondary px-8 py-4 text-lg font-semibold rounded-xl">
              Learn More 📚
            </button>
          </div>
        </div>
      </div>
    </div>

    {/* Features Section */}
    <div className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose GreenMate? 🌍
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover the features that make sustainable living easier and more rewarding
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="card-eco text-center p-8">
            <div className="text-4xl mb-4">🌱</div>
            <h3 className="text-xl font-semibold mb-4">Habit Tracking</h3>
            <p className="text-gray-600">
              Build sustainable habits with our intelligent tracking system and personalized recommendations.
            </p>
          </div>

          <div className="card-eco text-center p-8">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-xl font-semibold mb-4">Impact Analytics</h3>
            <p className="text-gray-600">
              Visualize your environmental impact with detailed analytics and progress tracking.
            </p>
          </div>

          <div className="card-eco text-center p-8">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold mb-4">AI Assistant</h3>
            <p className="text-gray-600">
              Get personalized eco-friendly tips and guidance from our intelligent AI assistant.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Simple Login component
const Login = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="card max-w-md w-full">
      <h1 className="text-2xl font-bold text-center mb-6">Login to GreenMate</h1>
      <form className="space-y-4">
        <input
          type="email"
          placeholder="Email"
          className="input-field w-full"
        />
        <input
          type="password"
          placeholder="Password"
          className="input-field w-full"
        />
        <button className="btn-eco w-full">
          Sign In
        </button>
      </form>
    </div>
  </div>
);

// Simple Profile component
const Profile = () => (
  <div className="min-h-screen bg-gray-50 py-8">
    <div className="max-w-4xl mx-auto px-4">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Profile 👤</h1>
      <div className="card">
        <p className="text-gray-600">Profile page is working! ✅</p>
      </div>
    </div>
  </div>
);

// Simple Settings component
const Settings = () => (
  <div className="min-h-screen bg-gray-50 py-8">
    <div className="max-w-4xl mx-auto px-4">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Settings ⚙️</h1>
      <div className="card">
        <p className="text-gray-600">Settings page is working! ✅</p>
      </div>
    </div>
  </div>
);

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <main>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </main>

          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
