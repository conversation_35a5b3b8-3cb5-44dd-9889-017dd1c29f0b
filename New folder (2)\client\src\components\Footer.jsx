import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ram, FaHear<PERSON> } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="relative bg-black/20 backdrop-blur-md border-t border-white/10 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-6 group">
              <div className="relative">
                <FaLeaf className="text-3xl text-green-400 group-hover:text-green-300 transition-all duration-300 group-hover:rotate-12" />
                <div className="absolute inset-0 text-3xl text-green-400 opacity-30 group-hover:opacity-60 transition-opacity duration-300 animate-pulse"></div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-white to-green-100 bg-clip-text text-transparent">
                GreenMate
              </span>
            </div>
            <p className="text-white/80 mb-6 max-w-md text-lg leading-relaxed">
              Your personal eco-friendly planner helping you build sustainable habits
              and make a positive impact on our planet. 🌍
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-white/60 hover:text-green-400 transition-all duration-300 hover:scale-110 hover-glow">
                <FaGithub size={24} />
              </a>
              <a href="#" className="text-white/60 hover:text-green-400 transition-all duration-300 hover:scale-110 hover-glow">
                <FaTwitter size={24} />
              </a>
              <a href="#" className="text-white/60 hover:text-green-400 transition-all duration-300 hover:scale-110 hover-glow">
                <FaInstagram size={24} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-green-300">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/dashboard" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link to="/habits" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Habits
                </Link>
              </li>
              <li>
                <Link to="/community" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Community
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-green-300">Support</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Help Center
                </a>
              </li>
              <li>
                <a href="#" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Terms of Service
                </a>
              </li>
              <li>
                <a href="#" className="text-white/70 hover:text-green-400 transition-all duration-300 hover:translate-x-2 inline-block">
                  Contact Us
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-white/10 mt-12 pt-8 text-center">
          <p className="text-white/70 flex items-center justify-center space-x-2 text-lg mb-3">
            <span>Made with</span>
            <FaHeart className="text-red-400 animate-pulse" />
            <span>for a sustainable future</span>
          </p>
          <p className="text-white/50 text-sm">
            © 2024 GreenMate. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
