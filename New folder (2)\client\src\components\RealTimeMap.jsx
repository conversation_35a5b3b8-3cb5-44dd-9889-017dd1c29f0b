import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { FaDirections, FaSync, FaSearch } from 'react-icons/fa';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import mapService from '../services/mapService';
import toast from 'react-hot-toast';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different place types
const createCustomIcon = (type, color = '#22c55e') => {
  const icons = {
    ev_charging: '🔌',
    recycling_center: '♻️',
    organic_shop: '🥬',
    renewable_energy: '☀️',
    sustainable_transport: '🚲',
    farmers_market: '🥕',
    sustainable_business: '🏪',
    user_location: '📍'
  };

  const emoji = icons[type] || '🌍';
  
  return L.divIcon({
    html: `
      <div style="
        background: ${color};
        border: 3px solid white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        cursor: pointer;
      ">
        ${emoji}
      </div>
    `,
    className: 'custom-marker',
    iconSize: [40, 40],
    iconAnchor: [20, 20],
    popupAnchor: [0, -20]
  });
};

// Component to center map on location
const MapController = ({ center, zoom }) => {
  const map = useMap();

  useEffect(() => {
    if (center) {
      map.setView(center, zoom);
    }
  }, [center, zoom, map]);

  return null;
};

const RealTimeMap = ({ 
  onPlaceClick, 
  height = '600px'
}) => {
  const [places, setPlaces] = useState([]);
  const [userLocation, setUserLocation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [mapCenter, setMapCenter] = useState([37.7749, -122.4194]); // Default to San Francisco
  const [mapZoom, setMapZoom] = useState(10);
  const [selectedFilters, setSelectedFilters] = useState(['ev_charging', 'recycling_center', 'organic_shop']);
  const [searchQuery, setSearchQuery] = useState('');

  const filters = [
    { id: 'ev_charging', label: 'EV Charging', icon: '🔌', color: '#3b82f6' },
    { id: 'recycling_center', label: 'Recycling', icon: '♻️', color: '#10b981' },
    { id: 'organic_shop', label: 'Organic Shops', icon: '🥬', color: '#84cc16' },
    { id: 'renewable_energy', label: 'Renewable Energy', icon: '☀️', color: '#f59e0b' },
    { id: 'sustainable_transport', label: 'Bike Stations', icon: '🚲', color: '#8b5cf6' },
    { id: 'farmers_market', label: 'Markets', icon: '🥕', color: '#ef4444' }
  ];

  useEffect(() => {
    getCurrentLocation();
  }, []);

  useEffect(() => {
    if (userLocation) {
      fetchNearbyPlaces();
    }
  }, [userLocation, selectedFilters]);

  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      const location = await mapService.getCurrentLocation();
      setUserLocation(location);
      setMapCenter([location.lat, location.lng]);
      setMapZoom(13);
      toast.success('📍 Location found!');
    } catch (error) {
      console.error('Location error:', error);
      toast.error('Could not get your location. Using default location.');
      // Use default location (San Francisco)
      setUserLocation({ lat: 37.7749, lng: -122.4194 });
      setMapCenter([37.7749, -122.4194]);
    } finally {
      setLoading(false);
    }
  };

  const fetchNearbyPlaces = async () => {
    if (!userLocation) return;

    try {
      setLoading(true);
      const nearbyPlaces = await mapService.getEcoFriendlyPlaces(
        userLocation.lat, 
        userLocation.lng, 
        5000 // 5km radius
      );

      // Filter places based on selected filters
      const filteredPlaces = nearbyPlaces.filter(place => 
        selectedFilters.includes(place.type)
      );

      // Add distance to each place
      const placesWithDistance = filteredPlaces.map(place => ({
        ...place,
        distance: mapService.calculateDistance(
          userLocation.lat,
          userLocation.lng,
          place.coordinates[1],
          place.coordinates[0]
        )
      }));

      // Sort by distance
      placesWithDistance.sort((a, b) => a.distance - b.distance);

      setPlaces(placesWithDistance);
      toast.success(`🌍 Found ${placesWithDistance.length} eco-friendly places nearby!`);
    } catch (error) {
      console.error('Error fetching places:', error);
      toast.error('Failed to load nearby places');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterId) => {
    setSelectedFilters(prev => 
      prev.includes(filterId)
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    );
  };

  const getDirections = (place) => {
    if (userLocation) {
      const url = mapService.getDirectionsUrl(
        userLocation.lat,
        userLocation.lng,
        place.coordinates[1],
        place.coordinates[0]
      );
      window.open(url, '_blank');
    }
  };

  const filteredPlaces = places.filter(place => 
    selectedFilters.includes(place.type)
  );

  return (
    <div className="relative">
      {/* Search and Filters */}
      <div className="mb-4 space-y-4">
        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2">
          {filters.map(filter => (
            <button
              key={filter.id}
              onClick={() => handleFilterChange(filter.id)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2 ${
                selectedFilters.includes(filter.id)
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <span>{filter.icon}</span>
              <span>{filter.label}</span>
            </button>
          ))}
          <button
            onClick={fetchNearbyPlaces}
            disabled={loading}
            className="btn-eco flex items-center space-x-2"
          >
            <FaSync className={loading ? 'animate-spin' : ''} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative">
        <MapContainer
          center={mapCenter}
          zoom={mapZoom}
          style={{ height, width: '100%' }}
          className="rounded-lg shadow-lg z-0"
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <MapController center={mapCenter} zoom={mapZoom} />
          
          {/* User Location Marker */}
          {userLocation && (
            <Marker 
              position={[userLocation.lat, userLocation.lng]} 
              icon={createCustomIcon('user_location', '#3b82f6')}
            >
              <Popup>
                <div className="text-center">
                  <strong>📍 Your Location</strong>
                  <br />
                  <small>Lat: {userLocation.lat.toFixed(4)}, Lng: {userLocation.lng.toFixed(4)}</small>
                </div>
              </Popup>
            </Marker>
          )}
          
          {/* Place Markers */}
          {filteredPlaces.map((place) => {
            const [lng, lat] = place.coordinates;
            const filter = filters.find(f => f.id === place.type);
            
            return (
              <Marker
                key={place.id}
                position={[lat, lng]}
                icon={createCustomIcon(place.type, filter?.color)}
              >
                <Popup maxWidth={300} className="custom-popup">
                  <div className="p-2">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-bold text-gray-900 text-sm">{place.name}</h3>
                      <span className="text-lg">{filter?.icon}</span>
                    </div>
                    
                    <p className="text-gray-600 text-xs mb-2">{place.description}</p>
                    
                    <div className="text-xs text-gray-500 mb-2">
                      📍 {place.address.formatted}
                    </div>
                    
                    {place.hours && (
                      <div className="text-xs text-gray-500 mb-2">
                        🕒 {place.hours}
                      </div>
                    )}
                    
                    {place.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {place.tags.map(tag => (
                          <span key={tag} className="bg-green-100 text-green-800 px-1 py-0.5 rounded text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => onPlaceClick && onPlaceClick(place)}
                        className="flex-1 bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600"
                      >
                        View Details
                      </button>
                      <button
                        onClick={() => getDirections(place)}
                        className="flex-1 bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
                      >
                        <FaDirections className="inline mr-1" />
                        Directions
                      </button>
                    </div>
                    
                    {place.distance && (
                      <div className="text-center mt-2 text-xs text-gray-500">
                        📍 {place.distance.toFixed(1)} km away
                      </div>
                    )}
                    
                    <div className="text-center mt-1 text-xs text-blue-500">
                      🌍 Real-time data from OpenStreetMap
                    </div>
                  </div>
                </Popup>
              </Marker>
            );
          })}
        </MapContainer>
        
        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 rounded-lg">
            <div className="bg-white p-4 rounded-lg flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
              <span>Loading eco-friendly places...</span>
            </div>
          </div>
        )}
        
        {/* Map Controls */}
        <div className="absolute top-4 right-4 z-10 space-y-2">
          <button
            onClick={getCurrentLocation}
            disabled={loading}
            className="bg-white p-2 rounded-lg shadow-lg hover:bg-gray-50 transition-colors"
            title="Find my location"
          >
            📍
          </button>
        </div>
        
        {/* Stats */}
        <div className="absolute bottom-4 left-4 z-10 bg-white p-3 rounded-lg shadow-lg">
          <div className="text-sm">
            <div className="font-semibold text-gray-900">
              🌍 {filteredPlaces.length} eco-friendly places found
            </div>
            <div className="text-xs text-gray-600 mt-1">
              Real-time data from OpenStreetMap
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeMap;
