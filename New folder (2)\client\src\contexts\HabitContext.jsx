import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useAuth } from './AuthContext';

const HabitContext = createContext();

// Habit reducer
const habitReducer = (state, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_HABITS':
      return { ...state, habits: action.payload, loading: false };
    case 'ADD_HABIT':
      return { ...state, habits: [...state.habits, action.payload] };
    case 'UPDATE_HABIT':
      return {
        ...state,
        habits: state.habits.map(habit =>
          habit._id === action.payload._id ? action.payload : habit
        )
      };
    case 'DELETE_HABIT':
      return {
        ...state,
        habits: state.habits.filter(habit => habit._id !== action.payload)
      };
    case 'SET_TRACKING':
      return { ...state, todayTracking: action.payload };
    case 'ADD_TRACKING':
      return {
        ...state,
        todayTracking: [...state.todayTracking, action.payload]
      };
    case 'UPDATE_TRACKING':
      return {
        ...state,
        todayTracking: state.todayTracking.map(tracking =>
          tracking._id === action.payload._id ? action.payload : tracking
        )
      };
    case 'SET_STREAK_DATA':
      return { ...state, streakData: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  habits: [],
  todayTracking: [],
  streakData: {},
  loading: false,
  error: null
};

export const HabitProvider = ({ children }) => {
  const [state, dispatch] = useReducer(habitReducer, initialState);
  const { isAuthenticated, user } = useAuth();

  // Fetch user's habits
  const fetchHabits = async () => {
    if (!isAuthenticated) return;

    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await axios.get('/habits/user');
      dispatch({ type: 'SET_HABITS', payload: response.data.habits });
    } catch (error) {
      console.error('Error fetching habits:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to fetch habits' });
    }
  };

  // Fetch today's tracking
  const fetchTodayTracking = async () => {
    if (!isAuthenticated) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const response = await axios.get(`/tracking/date/${today}`);
      dispatch({ type: 'SET_TRACKING', payload: response.data.tracking });
    } catch (error) {
      console.error('Error fetching today tracking:', error);
    }
  };

  // Add new habit
  const addHabit = async (habitData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await axios.post('/habits', habitData);
      
      dispatch({ type: 'ADD_HABIT', payload: response.data.habit });
      toast.success('Habit added successfully! 🌱');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to add habit';
      dispatch({ type: 'SET_ERROR', payload: message });
      toast.error(message);
      return { success: false, error: message };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Complete habit for today
  const completeHabit = async (habitId) => {
    try {
      const response = await axios.post('/tracking/complete', {
        habitId,
        date: new Date().toISOString().split('T')[0]
      });

      dispatch({ type: 'ADD_TRACKING', payload: response.data.tracking });
      toast.success('Habit completed! 🎉');

      // Update user stats if provided
      if (response.data.userStats) {
        // This would update the user context
      }

      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to complete habit';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Get habit completion status for today
  const isHabitCompletedToday = (habitId) => {
    return state.todayTracking.some(
      tracking => tracking.habit._id === habitId && tracking.completed
    );
  };

  // Get habit streak
  const getHabitStreak = (habitId) => {
    return state.streakData[habitId] || 0;
  };

  // Update habit
  const updateHabit = async (habitId, updates) => {
    try {
      const response = await axios.put(`/habits/${habitId}`, updates);
      
      dispatch({ type: 'UPDATE_HABIT', payload: response.data.habit });
      toast.success('Habit updated successfully! ✨');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to update habit';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Delete habit
  const deleteHabit = async (habitId) => {
    try {
      await axios.delete(`/habits/${habitId}`);
      
      dispatch({ type: 'DELETE_HABIT', payload: habitId });
      toast.success('Habit deleted successfully');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to delete habit';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  // Fetch streak data
  const fetchStreakData = async () => {
    if (!isAuthenticated) return;

    try {
      const response = await axios.get('/tracking/streaks');
      dispatch({ type: 'SET_STREAK_DATA', payload: response.data.streaks });
    } catch (error) {
      console.error('Error fetching streak data:', error);
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Load data when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchHabits();
      fetchTodayTracking();
      fetchStreakData();
    }
  }, [isAuthenticated, user]);

  const value = {
    ...state,
    addHabit,
    updateHabit,
    deleteHabit,
    completeHabit,
    isHabitCompletedToday,
    getHabitStreak,
    fetchHabits,
    fetchTodayTracking,
    fetchStreakData,
    clearError
  };

  return (
    <HabitContext.Provider value={value}>
      {children}
    </HabitContext.Provider>
  );
};

export const useHabits = () => {
  const context = useContext(HabitContext);
  if (!context) {
    throw new Error('useHabits must be used within a HabitProvider');
  }
  return context;
};
