import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

// Language translations
const translations = {
  en: {
    // Navigation
    home: 'Home',
    dashboard: 'Dashboard',
    habits: 'Habits',
    impact: 'Impact',
    community: 'Community',
    challenges: 'Challenges',
    map: 'Eco Map',
    aiAssistant: 'AI Assistant',
    profile: 'Profile',
    settings: 'Settings',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',

    // Common
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    remove: 'Remove',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    points: 'Points',
    level: 'Level',
    streak: 'Streak',
    
    // Home page
    welcomeTitle: 'Welcome to GreenMate',
    welcomeSubtitle: 'Your Personal Eco-Friendly Planner',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    
    // Habits
    dailyHabits: 'Daily Habits',
    markComplete: 'Mark Complete',
    habitCompleted: 'Habit Completed!',
    addHabit: 'Add New Habit',
    customHabit: 'Custom Habit',
    
    // Impact
    yourImpact: 'Your Environmental Impact',
    co2Saved: 'CO₂ Saved',
    waterSaved: 'Water Saved',
    electricitySaved: 'Electricity Saved',
    plasticAvoided: 'Plastic Avoided',
    
    // Community
    leaderboard: 'Leaderboard',
    ecoWarriors: 'Eco Warriors',
    followFriends: 'Follow Friends',
    
    // Challenges
    activeChallenges: 'Active Challenges',
    joinChallenge: 'Join Challenge',
    challengeProgress: 'Challenge Progress',
    
    // Units
    kg: 'kg',
    liters: 'liters',
    kwh: 'kWh',
    grams: 'grams',
    days: 'days',
    
    // Messages
    loginSuccess: 'Login successful!',
    registerSuccess: 'Registration successful!',
    habitAdded: 'Habit added successfully!',
    profileUpdated: 'Profile updated successfully!'
  },
  
  ta: {
    // Navigation (Tamil)
    home: 'முகப்பு',
    dashboard: 'டாஷ்போர்டு',
    habits: 'பழக்கங்கள்',
    impact: 'தாக்கம்',
    community: 'சமூகம்',
    challenges: 'சவால்கள்',
    map: 'சுற்றுச்சூழல் வரைபடம்',
    aiAssistant: 'AI உதவியாளர்',
    profile: 'சுயவிவரம்',
    settings: 'அமைப்புகள்',
    login: 'உள்நுழைய',
    register: 'பதிவு செய்ய',
    logout: 'வெளியேறு',

    // Common
    save: 'சேமி',
    cancel: 'ரத்து செய்',
    delete: 'நீக்கு',
    edit: 'திருத்து',
    add: 'சேர்',
    remove: 'அகற்று',
    loading: 'ஏற்றுகிறது...',
    error: 'பிழை',
    success: 'வெற்றி',
    points: 'புள்ளிகள்',
    level: 'நிலை',
    streak: 'தொடர்ச்சி',
    
    // Home page
    welcomeTitle: 'GreenMate-க்கு வரவேற்கிறோம்',
    welcomeSubtitle: 'உங்கள் தனிப்பட்ட சுற்றுச்சூழல் நட்பு திட்டமிடுபவர்',
    getStarted: 'தொடங்குங்கள்',
    learnMore: 'மேலும் அறிய',
    
    // Habits
    dailyHabits: 'தினசரி பழக்கங்கள்',
    markComplete: 'முடிந்ததாக குறிக்கவும்',
    habitCompleted: 'பழக்கம் முடிந்தது!',
    addHabit: 'புதிய பழக்கம் சேர்க்கவும்',
    customHabit: 'தனிப்பயன் பழக்கம்',
    
    // Impact
    yourImpact: 'உங்கள் சுற்றுச்சூழல் தாக்கம்',
    co2Saved: 'CO₂ சேமிப்பு',
    waterSaved: 'நீர் சேமிப்பு',
    electricitySaved: 'மின்சாரம் சேமிப்பு',
    plasticAvoided: 'பிளாஸ்டிக் தவிர்ப்பு',
    
    // Community
    leaderboard: 'தலைமை பலகை',
    ecoWarriors: 'சுற்றுச்சூழல் வீரர்கள்',
    followFriends: 'நண்பர்களை பின்தொடரவும்',
    
    // Challenges
    activeChallenges: 'செயலில் உள்ள சவால்கள்',
    joinChallenge: 'சவாலில் சேரவும்',
    challengeProgress: 'சவால் முன்னேற்றம்',
    
    // Units
    kg: 'கிலோ',
    liters: 'லிட்டர்',
    kwh: 'kWh',
    grams: 'கிராம்',
    days: 'நாட்கள்',
    
    // Messages
    loginSuccess: 'உள்நுழைவு வெற்றிகரமாக!',
    registerSuccess: 'பதிவு வெற்றிகரமாக!',
    habitAdded: 'பழக்கம் வெற்றிகரமாக சேர்க்கப்பட்டது!',
    profileUpdated: 'சுயவிவரம் வெற்றிகரமாக புதுப்பிக்கப்பட்டது!'
  },
  
  tanglish: {
    // Navigation (Tanglish - Tamil + English mix)
    home: 'Home',
    dashboard: 'Dashboard',
    habits: 'Habits',
    impact: 'Impact',
    community: 'Community',
    challenges: 'Challenges',
    map: 'Eco Map',
    aiAssistant: 'AI Assistant',
    profile: 'Profile',
    settings: 'Settings',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',

    // Common
    save: 'Save pannu',
    cancel: 'Cancel pannu',
    delete: 'Delete pannu',
    edit: 'Edit pannu',
    add: 'Add pannu',
    remove: 'Remove pannu',
    loading: 'Loading aaguthu...',
    error: 'Error',
    success: 'Success',
    points: 'Points',
    level: 'Level',
    streak: 'Streak',
    
    // Home page
    welcomeTitle: 'GreenMate-ku Welcome!',
    welcomeSubtitle: 'Unga Personal Eco-Friendly Planner',
    getStarted: 'Start pannalam',
    learnMore: 'More therinjukalam',
    
    // Habits
    dailyHabits: 'Daily Habits',
    markComplete: 'Complete aana mark pannu',
    habitCompleted: 'Habit complete aayiduchu!',
    addHabit: 'Pudhusa habit add pannu',
    customHabit: 'Custom Habit',
    
    // Impact
    yourImpact: 'Unga Environmental Impact',
    co2Saved: 'CO₂ Save panninga',
    waterSaved: 'Water Save panninga',
    electricitySaved: 'Current Save panninga',
    plasticAvoided: 'Plastic Avoid panninga',
    
    // Community
    leaderboard: 'Leaderboard',
    ecoWarriors: 'Eco Warriors',
    followFriends: 'Friends-a follow pannu',
    
    // Challenges
    activeChallenges: 'Active Challenges',
    joinChallenge: 'Challenge-la join aagu',
    challengeProgress: 'Challenge Progress',
    
    // Units
    kg: 'kg',
    liters: 'liters',
    kwh: 'kWh',
    grams: 'grams',
    days: 'days',
    
    // Messages
    loginSuccess: 'Login successful aayiduchu!',
    registerSuccess: 'Register successful aayiduchu!',
    habitAdded: 'Habit successfully add aayiduchu!',
    profileUpdated: 'Profile successfully update aayiduchu!'
  }
};

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    return localStorage.getItem('language') || 'en';
  });

  useEffect(() => {
    localStorage.setItem('language', currentLanguage);
  }, [currentLanguage]);

  const changeLanguage = (language) => {
    if (translations[language]) {
      setCurrentLanguage(language);
    }
  };

  const t = (key) => {
    return translations[currentLanguage][key] || translations.en[key] || key;
  };

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    availableLanguages: [
      { code: 'en', name: 'English', flag: '🇺🇸' },
      { code: 'ta', name: 'தமிழ்', flag: '🇮🇳' },
      { code: 'tanglish', name: 'Tanglish', flag: '🌍' }
    ]
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
