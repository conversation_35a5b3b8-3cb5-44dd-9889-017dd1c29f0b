@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f9fafb;
    color: #111827;
    line-height: 1.6;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Enhanced Button Components */
  .btn-primary {
    background-color: #059669;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }

  .btn-primary:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    background-color: #e5e7eb;
    color: #374151;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }

  .btn-secondary:hover {
    background-color: #d1d5db;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .btn-eco {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.15);
  }

  .btn-eco:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 25px 0 rgba(34, 197, 94, 0.2);
  }

  .btn-outline {
    background-color: transparent;
    color: #374151;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }

  .btn-outline:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
  }

  /* Enhanced Card Components */
  .card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
  }

  .card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .card-eco {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.15);
    border: 1px solid #bbf7d0;
    transition: all 0.2s;
    position: relative;
    overflow: hidden;
  }

  .card-eco:hover {
    box-shadow: 0 10px 25px 0 rgba(34, 197, 94, 0.2);
    border-color: #86efac;
    transform: translateY(-2px);
  }

  .card-eco::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #10b981, #059669, #047857);
  }

  /* Enhanced Input Components */
  .input-field {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s;
    background-color: white;
  }

  .input-field:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .input-eco {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s;
    background-color: white;
  }

  .input-eco:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  /* Enhanced Utility Components */
  .bg-gradient-eco {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }

  .hover-lift {
    transition: transform 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }

  /* Enhanced Form Components */
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .form-error {
    font-size: 0.875rem;
    color: #dc2626;
    margin-top: 0.25rem;
  }

  .form-success {
    font-size: 0.875rem;
    color: #059669;
    margin-top: 0.25rem;
  }

  /* Enhanced Badge Components */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-green {
    background-color: #d1fae5;
    color: #065f46;
  }

  .badge-blue {
    background-color: #dbeafe;
    color: #1e3a8a;
  }

  .badge-yellow {
    background-color: #fef3c7;
    color: #92400e;
  }

  .badge-red {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .badge-gray {
    background-color: #f3f4f6;
    color: #374151;
  }

  /* Enhanced Avatar Components */
  .avatar {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    color: white;
  }

  .avatar-sm {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .avatar-md {
    width: 3rem;
    height: 3rem;
    font-size: 1.125rem;
  }

  .avatar-lg {
    width: 4rem;
    height: 4rem;
    font-size: 1.5rem;
  }

  .avatar-xl {
    width: 6rem;
    height: 6rem;
    font-size: 1.875rem;
  }

  /* Enhanced Progress Components */
  .progress-bar {
    width: 100%;
    background-color: #e5e7eb;
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    border-radius: 9999px;
    transition: all 0.5s ease-out;
  }

  .progress-green {
    background: linear-gradient(to right, #34d399, #059669);
  }

  .progress-blue {
    background: linear-gradient(to right, #60a5fa, #2563eb);
  }

  .progress-yellow {
    background: linear-gradient(to right, #fbbf24, #d97706);
  }

  /* Enhanced Status Indicators */
  .status-online {
    width: 0.75rem;
    height: 0.75rem;
    background-color: #10b981;
    border-radius: 50%;
  }

  .status-offline {
    width: 0.75rem;
    height: 0.75rem;
    background-color: #d1d5db;
    border-radius: 50%;
  }

  .status-away {
    width: 0.75rem;
    height: 0.75rem;
    background-color: #f59e0b;
    border-radius: 50%;
  }

  /* Enhanced Navigation Components */
  .nav-link {
    color: #4b5563;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;
    text-decoration: none;
  }

  .nav-link:hover {
    color: #059669;
  }

  .nav-link-active {
    color: #059669;
    background-color: #ecfdf5;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Enhanced Tab Components */
  .tab-nav {
    display: flex;
    background-color: white;
    border-radius: 0.5rem;
    padding: 0.25rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }

  .tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    background: none;
  }

  .tab-button-active {
    background-color: #059669;
    color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .tab-button-inactive {
    color: #4b5563;
  }

  .tab-button-inactive:hover {
    color: #059669;
    background-color: #ecfdf5;
  }

  /* Enhanced Modal Components */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    padding: 1rem;
  }

  .modal-content {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    max-width: 28rem;
    width: 100%;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: scale(1);
    transition: all 0.2s;
  }

  /* Enhanced Loading Components */
  .loading-spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    height: 2rem;
    width: 2rem;
    border-bottom: 2px solid #10b981;
    border-top: 2px solid transparent;
    border-left: 2px solid transparent;
    border-right: 2px solid transparent;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Enhanced Eco-themed Components */
  .eco-gradient {
    background: linear-gradient(to bottom right, #34d399, #22c55e, #0d9488);
  }

  .eco-text-gradient {
    background: linear-gradient(to right, #059669, #22c55e);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .eco-shadow {
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.2);
  }

  .eco-glow {
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  /* Enhanced Stats Components */
  .stat-card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: all 0.2s;
  }

  .stat-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: #d1d5db;
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #4b5563;
  }
}

/* Custom animations and utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent;
  }

  .shadow-eco {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
  }

  .shadow-eco-lg {
    box-shadow: 0 10px 25px 0 rgba(16, 185, 129, 0.2);
  }

  .border-eco {
    border-color: #10b981;
  }

  .ring-eco {
    --tw-ring-color: rgba(16, 185, 129, 0.2);
  }
}
