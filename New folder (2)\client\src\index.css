@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply m-0 p-0 box-border;
  }

  body {
    @apply font-sans antialiased bg-gray-50 text-gray-900 leading-relaxed;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* Enhanced Button Components */
  .btn-primary {
    @apply bg-emerald-600 text-white font-medium px-4 py-2 rounded-lg border-0 cursor-pointer transition-all duration-200;
    @apply inline-block text-center no-underline hover:bg-emerald-700 hover:-translate-y-0.5 hover:shadow-md;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-700 font-medium px-4 py-2 rounded-lg border-0 cursor-pointer transition-all duration-200;
    @apply inline-block text-center no-underline hover:bg-gray-300 hover:shadow-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-eco {
    @apply bg-gradient-to-r from-emerald-500 to-green-600 text-white font-medium px-4 py-2 rounded-lg border-0 cursor-pointer;
    @apply transition-all duration-200 inline-block text-center no-underline shadow-lg shadow-emerald-500/25;
    @apply hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/30 hover:from-emerald-600 hover:to-green-700;
    @apply focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2;
    @apply active:scale-100 active:shadow-lg;
  }

  .btn-outline {
    @apply bg-transparent text-gray-700 font-medium px-4 py-2 rounded-lg border border-gray-300 cursor-pointer;
    @apply transition-all duration-200 inline-block text-center no-underline hover:bg-gray-50 hover:border-gray-400;
    @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  /* Enhanced Card Components */
  .card {
    @apply bg-white rounded-xl p-6 shadow-sm border border-gray-200 transition-all duration-200;
    @apply hover:shadow-md hover:-translate-y-0.5;
  }

  .card-eco {
    @apply bg-white rounded-xl p-6 shadow-lg shadow-emerald-500/15 border border-emerald-200 transition-all duration-200;
    @apply hover:shadow-xl hover:shadow-emerald-500/20 hover:border-emerald-300 hover:-translate-y-1;
    @apply relative overflow-hidden;
  }

  .card-eco::before {
    @apply absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-emerald-500 to-green-600;
    content: '';
  }

  /* Enhanced Input Components */
  .input-field {
    @apply w-full px-3 py-3 border border-gray-300 rounded-lg text-base transition-all duration-200 bg-white;
    @apply focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-500/20;
    @apply placeholder:text-gray-400;
  }

  .input-eco {
    @apply w-full px-3 py-3 border border-gray-300 rounded-lg text-base transition-all duration-200 bg-white;
    @apply focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-500/20;
    @apply placeholder:text-gray-400;
  }

  /* Enhanced Utility Components */
  .bg-gradient-eco {
    @apply bg-gradient-to-r from-emerald-500 to-green-600;
  }

  .hover-lift {
    @apply transition-transform duration-200 ease-in-out hover:-translate-y-1;
  }

  .hover-scale {
    @apply transition-transform duration-200 ease-in-out hover:scale-105;
  }

  /* Enhanced Form Components */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-red-600 mt-1;
  }

  .form-success {
    @apply text-sm text-emerald-600 mt-1;
  }

  /* Enhanced Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-green {
    @apply bg-emerald-100 text-emerald-800;
  }

  .badge-blue {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-yellow {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-red {
    @apply bg-red-100 text-red-800;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }

  /* Enhanced Avatar Components */
  .avatar {
    @apply rounded-full flex items-center justify-center font-bold shadow-lg bg-gradient-eco text-white;
  }

  .avatar-sm {
    @apply w-8 h-8 text-sm;
  }

  .avatar-md {
    @apply w-12 h-12 text-lg;
  }

  .avatar-lg {
    @apply w-16 h-16 text-2xl;
  }

  .avatar-xl {
    @apply w-24 h-24 text-3xl;
  }

  /* Enhanced Progress Components */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
  }

  .progress-fill {
    @apply h-full rounded-full transition-all duration-500 ease-out;
  }

  .progress-green {
    @apply bg-gradient-to-r from-emerald-400 to-emerald-600;
  }

  .progress-blue {
    @apply bg-gradient-to-r from-blue-400 to-blue-600;
  }

  .progress-yellow {
    @apply bg-gradient-to-r from-yellow-400 to-yellow-600;
  }

  /* Enhanced Status Indicators */
  .status-online {
    @apply w-3 h-3 bg-emerald-500 rounded-full;
  }

  .status-offline {
    @apply w-3 h-3 bg-gray-300 rounded-full;
  }

  .status-away {
    @apply w-3 h-3 bg-yellow-500 rounded-full;
  }

  /* Enhanced Navigation Components */
  .nav-link {
    @apply text-gray-600 hover:text-emerald-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply text-emerald-600 bg-emerald-50 px-3 py-2 rounded-md text-sm font-medium;
  }

  /* Enhanced Tab Components */
  .tab-nav {
    @apply flex bg-white rounded-lg p-1 shadow border border-gray-200;
  }

  .tab-button {
    @apply flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .tab-button-active {
    @apply bg-emerald-600 text-white shadow-sm;
  }

  .tab-button-inactive {
    @apply text-gray-600 hover:text-emerald-600 hover:bg-emerald-50;
  }

  /* Enhanced Modal Components */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4;
  }

  .modal-content {
    @apply bg-white rounded-xl p-6 max-w-md w-full shadow-2xl transform transition-all duration-200;
  }

  /* Enhanced Loading Components */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500;
  }

  .loading-pulse {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Enhanced Eco-themed Components */
  .eco-gradient {
    @apply bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600;
  }

  .eco-text-gradient {
    @apply bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent;
  }

  .eco-shadow {
    @apply shadow-lg shadow-emerald-500/20;
  }

  .eco-glow {
    @apply shadow-lg shadow-emerald-500/30 ring-1 ring-emerald-500/20;
  }

  /* Enhanced Stats Components */
  .stat-card {
    @apply bg-white rounded-xl p-6 shadow-sm border border-gray-200 text-center transition-all duration-200;
    @apply hover:shadow-md hover:-translate-y-0.5 hover:border-gray-300;
  }

  .stat-value {
    @apply text-2xl font-bold mb-1;
  }

  .stat-label {
    @apply text-sm text-gray-600;
  }
}

/* Custom animations and utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent;
  }

  .shadow-eco {
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.15);
  }

  .shadow-eco-lg {
    box-shadow: 0 10px 25px 0 rgba(16, 185, 129, 0.2);
  }

  .border-eco {
    border-color: #10b981;
  }

  .ring-eco {
    --tw-ring-color: rgba(16, 185, 129, 0.2);
  }
}
