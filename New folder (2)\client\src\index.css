@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}

html {
  scroll-behavior: smooth;
}

/* Button styles */
.btn-primary {
  background-color: #059669;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary:hover {
  background-color: #047857;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #374151;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.btn-eco {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.15);
}

.btn-eco:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px 0 rgba(34, 197, 94, 0.2);
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: box-shadow 0.2s;
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card-eco {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.15);
  border: 1px solid #bbf7d0;
  padding: 1.5rem;
  transition: all 0.2s;
}

.card-eco:hover {
  box-shadow: 0 10px 25px 0 rgba(34, 197, 94, 0.2);
  border-color: #86efac;
}

/* Input styles */
.input-field {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.2s;
}

.input-field:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }

.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-green-600 { color: #059669; }
.text-white { color: white; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

.border { border: 1px solid #e5e7eb; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }

.transition-all { transition: all 0.2s; }
.transition-colors { transition: color 0.2s, background-color 0.2s; }

.cursor-pointer { cursor: pointer; }

.grid { display: grid; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }

/* Responsive grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Container */
.max-w-7xl { max-width: 80rem; margin: 0 auto; }
.max-w-6xl { max-width: 72rem; margin: 0 auto; }
.max-w-4xl { max-width: 56rem; margin: 0 auto; }
.max-w-md { max-width: 28rem; margin: 0 auto; }

.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

/* Essential eco-friendly classes for Profile and Settings pages */

/* Color Variables */
:root {
  --eco-forest-50: #f0fdf4;
  --eco-forest-100: #dcfce7;
  --eco-forest-500: #22c55e;
  --eco-mint-100: #ccfbef;
  --eco-sky-100: #e0f2fe;
  --eco-earth-100: #fef7cd;
  --neutral-50: #fafafa;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-600: #525252;
  --neutral-900: #171717;
  --success: #22c55e;
  --info: #0ea5e9;
}

/* Eco-friendly gradients */
.bg-gradient-nature { background: linear-gradient(135deg, #22c55e 0%, #14b8a6 50%, #0ea5e9 100%); }
.bg-gradient-forest { background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%); }
.bg-gradient-ocean { background: linear-gradient(135deg, #0ea5e9 0%, #14b8a6 100%); }

/* Essential utility classes */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1.5rem; }
.nature-texture { position: relative; }
.card-eco { background: linear-gradient(135deg, white 0%, #f0fdf4 100%); border: 1px solid #dcfce7; }
.card-forest { background: linear-gradient(135deg, #f0fdf4 0%, white 100%); border: 1px solid #dcfce7; }
.card-ocean { background: linear-gradient(135deg, #e0f2fe 0%, #ccfbef 100%); border: 1px solid #bae6fd; }

/* Spacing */
.py-xl { padding-top: 2rem; padding-bottom: 2rem; }
.mb-xl { margin-bottom: 2rem; }
.gap-xl { gap: 2rem; }
.gap-lg { gap: 1.5rem; }
.gap-md { gap: 1rem; }
.gap-sm { gap: 0.5rem; }
.gap-xs { gap: 0.25rem; }
.p-md { padding: 1rem; }
.p-lg { padding: 1.5rem; }
.mb-lg { margin-bottom: 1.5rem; }
.mb-md { margin-bottom: 1rem; }
.mb-sm { margin-bottom: 0.5rem; }
.mb-xs { margin-bottom: 0.25rem; }
.mt-xl { margin-top: 2rem; }
.space-y-lg > * + * { margin-top: 1.5rem; }

/* Colors */
.text-eco-forest { color: #16a34a; }
.text-eco-mint { color: #14b8a6; }
.text-eco-sky { color: #0ea5e9; }
.text-eco-earth { color: #eab308; }
.text-neutral-900 { color: #171717; }
.text-neutral-600 { color: #525252; }
.text-neutral-500 { color: #737373; }
.bg-eco-forest-100 { background-color: #dcfce7; }
.bg-eco-mint-100 { background-color: #ccfbef; }
.bg-eco-sky-100 { background-color: #e0f2fe; }
.bg-eco-earth-100 { background-color: #fef7cd; }
.bg-eco-forest-500 { background-color: #22c55e; }
.bg-success { background-color: #22c55e; }
.bg-info { background-color: #0ea5e9; }
.bg-opacity-50 { background-color: rgba(255, 255, 255, 0.5); }

/* Red colors for danger actions */
.bg-red-50 { background-color: #fef2f2; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }
.border-red-200 { border-color: #fecaca; }
.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-red-900 { color: #7f1d1d; }
.hover\\:bg-red-600:hover { background-color: #dc2626; }

/* Buttons */
.btn { display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.5rem 1rem; border-radius: 0.75rem; font-weight: 500; border: 1px solid transparent; cursor: pointer; transition: all 0.15s; }
.btn-nature { background: linear-gradient(135deg, #22c55e 0%, #14b8a6 50%, #0ea5e9 100%); color: white; }
.btn-outline { background: transparent; color: #16a34a; border-color: #dcfce7; }
.btn-outline:hover { background: #f0fdf4; border-color: #22c55e; }

/* Layout */
.lg\\:col-span-1 { grid-column: span 1; }
.lg\\:col-span-3 { grid-column: span 3; }
.sticky { position: sticky; }
.top-4 { top: 1rem; }

/* Dimensions */
.w-8 { width: 2rem; }
.w-11 { width: 2.75rem; }
.w-32 { width: 8rem; }
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }

/* Transform */
.translate-x-1 { transform: translateX(0.25rem); }
.translate-x-6 { transform: translateX(1.5rem); }

/* Animations */
@keyframes spin { to { transform: rotate(360deg); } }
@keyframes breathe { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.03); } }
@keyframes float-gentle { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-8px); } }
@keyframes slide-up-fade { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
@keyframes leaf-grow { 0% { transform: scale(0); opacity: 0; } 100% { transform: scale(1); opacity: 1; } }
@keyframes wave-subtle { 0%, 100% { transform: rotate(0deg); } 25% { transform: rotate(1deg); } 75% { transform: rotate(-1deg); } }
@keyframes pulse-eco { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.02); } }

.animate-spin { animation: spin 1s linear infinite; }
.animate-breathe { animation: breathe 4s ease-in-out infinite; }
.animate-float-gentle { animation: float-gentle 3s ease-in-out infinite; }
.animate-slide-up-fade { animation: slide-up-fade 0.5s ease-out; }
.animate-leaf-grow { animation: leaf-grow 0.6s ease-out; }
.animate-wave-subtle { animation: wave-subtle 2s ease-in-out infinite; }
.animate-pulse-eco { animation: pulse-eco 2s infinite; }

/* Hover effects */
.hover-lift:hover { transform: translateY(-4px); transition: all 0.25s; }
.hover-glow:hover { box-shadow: 0 10px 15px -3px rgba(34, 197, 94, 0.1); transition: all 0.25s; }

/* Typography */
.font-display { font-family: 'Inter', sans-serif; }
.leading-relaxed { line-height: 1.625; }
.resize-none { resize: none; }

/* Progress bars */
.progress-bar { width: 100%; height: 8px; background: #e5e5e5; border-radius: 0.5rem; overflow: hidden; }
.progress-fill { height: 100%; background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%); border-radius: 0.5rem; transition: width 0.35s; }
.progress-fill-nature { background: linear-gradient(135deg, #22c55e 0%, #14b8a6 50%, #0ea5e9 100%); }
.progress-fill-forest { background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%); }
.progress-fill-earth { background: linear-gradient(135deg, #eab308 0%, #22c55e 100%); }

/* Badges */
.badge { display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; }
.badge-sm { padding: 2px 0.25rem; font-size: 0.625rem; }
.badge-nature { background: linear-gradient(135deg, #22c55e 0%, #14b8a6 100%); color: white; }
.badge-success { background-color: #dcfce7; color: #15803d; }
.badge-info { background-color: #e0f2fe; color: #075985; }
.badge-warning { background-color: #fef7cd; color: #a16207; }
.badge-secondary { background-color: #f5f5f5; color: #525252; }

/* Responsive */
@media (min-width: 1024px) {
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 768px) {
  .md\\:flex-row { flex-direction: column; }
  .md\\:grid-cols-2 { grid-template-columns: 1fr; }
  .md\\:grid-cols-3 { grid-template-columns: 1fr; }
  .md\\:grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
}
