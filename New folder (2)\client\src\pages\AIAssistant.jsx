import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPaperPlane, FaRobot, FaUser, FaLightbulb, FaRecycle, FaLeaf, FaCopy, FaThumbsUp, FaThumbsDown } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';
import toast from 'react-hot-toast';

const AIAssistant = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();
  const { t } = useLanguage();

  const quickSuggestions = [
    {
      icon: '🌱',
      text: 'Suggest eco-friendly habits for beginners',
      category: 'habits'
    },
    {
      icon: '♻️',
      text: 'How can I reduce waste at home?',
      category: 'waste'
    },
    {
      icon: '💡',
      text: 'Energy saving tips for my apartment',
      category: 'energy'
    },
    {
      icon: '🚗',
      text: 'Sustainable transportation options',
      category: 'transport'
    },
    {
      icon: '🥗',
      text: 'Eco-friendly food choices',
      category: 'food'
    },
    {
      icon: '🏠',
      text: 'Make my home more sustainable',
      category: 'home'
    }
  ];

  useEffect(() => {
    // Add welcome message
    setMessages([
      {
        id: 1,
        type: 'ai',
        content: `Hello ${user?.name || 'there'}! 👋 I'm your AI eco-assistant. I'm here to help you live a more sustainable lifestyle. Ask me anything about eco-friendly habits, environmental tips, or sustainable living!`,
        timestamp: new Date(),
        suggestions: [
          'What are some easy eco habits to start with?',
          'How can I calculate my carbon footprint?',
          'Suggest sustainable alternatives for daily items'
        ]
      }
    ]);

    fetchPersonalizedSuggestions();
    scrollToBottom();
  }, [user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchPersonalizedSuggestions = async () => {
    try {
      const response = await axios.get('/ai/suggestions');
      setSuggestions(response.data.suggestions);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
  };

  const sendMessage = async (messageText = inputMessage) => {
    if (!messageText.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);

    try {
      const response = await axios.post('/ai/chat', {
        message: messageText,
        context: {
          userHabits: user?.habits || [],
          userLevel: user?.ecoProfile?.level || 1,
          userPoints: user?.ecoProfile?.totalPoints || 0
        }
      });

      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: response.data.response,
        timestamp: new Date(),
        suggestions: response.data.suggestions || [],
        actionItems: response.data.actionItems || []
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date(),
        isError: true
      };
      setMessages(prev => [...prev, errorMessage]);
      toast.error('Failed to get AI response');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const copyMessage = (content) => {
    navigator.clipboard.writeText(content);
    toast.success('Message copied to clipboard!');
  };

  const rateMessage = async (messageId, rating) => {
    try {
      await axios.post('/ai/feedback', {
        messageId,
        rating,
        userId: user?.id
      });
      toast.success('Thank you for your feedback!');
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Eco Assistant 🤖</h1>
          <p className="text-gray-600">Get personalized advice for sustainable living</p>
        </div>

        {/* Chat Container */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Messages Area */}
          <div className="h-96 overflow-y-auto p-6 space-y-4">
            <AnimatePresence>
              {messages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  onSendMessage={sendMessage}
                  onCopy={copyMessage}
                  onRate={rateMessage}
                />
              ))}
            </AnimatePresence>

            {loading && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center space-x-2 text-gray-500"
              >
                <FaRobot className="text-green-500" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span>AI is thinking...</span>
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Quick Suggestions */}
          {messages.length === 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Quick suggestions:</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {quickSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => sendMessage(suggestion.text)}
                    className="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                  >
                    <span className="mr-2">{suggestion.icon}</span>
                    {suggestion.text}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input Area */}
          <div className="p-6 border-t border-gray-200">
            <div className="flex space-x-4">
              <div className="flex-1">
                <textarea
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about sustainable living..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  rows="2"
                  disabled={loading}
                />
              </div>
              <button
                onClick={() => sendMessage()}
                disabled={loading || !inputMessage.trim()}
                className="btn-eco px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaPaperPlane />
              </button>
            </div>
          </div>
        </div>

        {/* Personalized Suggestions */}
        {suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 bg-white rounded-xl shadow-lg p-6"
          >
            <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
              <FaLightbulb className="text-yellow-500 mr-2" />
              Personalized Suggestions
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              {suggestions.map((suggestion, index) => (
                <SuggestionCard
                  key={index}
                  suggestion={suggestion}
                  onAskMore={(question) => sendMessage(question)}
                />
              ))}
            </div>
          </motion.div>
        )}

        {/* Tips Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mt-8 grid md:grid-cols-3 gap-6"
        >
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <FaLeaf className="text-4xl text-green-500 mx-auto mb-4" />
            <h3 className="font-bold text-gray-900 mb-2">Habit Recommendations</h3>
            <p className="text-gray-600 text-sm">Get personalized eco-habit suggestions based on your lifestyle</p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <FaRecycle className="text-4xl text-blue-500 mx-auto mb-4" />
            <h3 className="font-bold text-gray-900 mb-2">Waste Reduction</h3>
            <p className="text-gray-600 text-sm">Learn practical ways to minimize waste in your daily life</p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <FaLightbulb className="text-4xl text-yellow-500 mx-auto mb-4" />
            <h3 className="font-bold text-gray-900 mb-2">Energy Efficiency</h3>
            <p className="text-gray-600 text-sm">Discover tips to reduce energy consumption and save money</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

// Message Bubble Component
const MessageBubble = ({ message, onSendMessage, onCopy, onRate }) => {
  const isUser = message.type === 'user';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`max-w-xs lg:max-w-md ${isUser ? 'order-2' : 'order-1'}`}>
        <div className={`flex items-start space-x-2 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-green-500' : 'bg-gray-300'
          }`}>
            {isUser ? <FaUser className="text-white text-sm" /> : <FaRobot className="text-gray-600 text-sm" />}
          </div>

          <div className={`px-4 py-2 rounded-lg ${
            isUser
              ? 'bg-green-500 text-white'
              : message.isError
              ? 'bg-red-100 text-red-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>

            {message.suggestions && message.suggestions.length > 0 && (
              <div className="mt-3 space-y-2">
                <p className="text-xs font-medium opacity-75">Suggested follow-ups:</p>
                {message.suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => onSendMessage(suggestion)}
                    className="block w-full text-left text-xs bg-white bg-opacity-20 hover:bg-opacity-30 rounded px-2 py-1 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            )}

            {message.actionItems && message.actionItems.length > 0 && (
              <div className="mt-3">
                <p className="text-xs font-medium opacity-75 mb-2">Action items:</p>
                <ul className="text-xs space-y-1">
                  {message.actionItems.map((item, index) => (
                    <li key={index} className="flex items-start space-x-1">
                      <span>•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {!isUser && (
          <div className="flex items-center space-x-2 mt-2 ml-10">
            <button
              onClick={() => onCopy(message.content)}
              className="text-gray-400 hover:text-gray-600 text-xs"
              title="Copy message"
            >
              <FaCopy />
            </button>
            <button
              onClick={() => onRate(message.id, 'positive')}
              className="text-gray-400 hover:text-green-600 text-xs"
              title="Helpful"
            >
              <FaThumbsUp />
            </button>
            <button
              onClick={() => onRate(message.id, 'negative')}
              className="text-gray-400 hover:text-red-600 text-xs"
              title="Not helpful"
            >
              <FaThumbsDown />
            </button>
            <span className="text-xs text-gray-400">
              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Suggestion Card Component
const SuggestionCard = ({ suggestion, onAskMore }) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="flex items-start space-x-3">
        <span className="text-2xl">{suggestion.icon}</span>
        <div className="flex-1">
          <h4 className="font-semibold text-gray-900 mb-1">{suggestion.title}</h4>
          <p className="text-gray-600 text-sm mb-3">{suggestion.description}</p>
          <button
            onClick={() => onAskMore(`Tell me more about ${suggestion.title.toLowerCase()}`)}
            className="text-green-600 hover:text-green-700 text-sm font-medium"
          >
            Learn more →
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
