import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTrophy, FaUsers, FaFire, FaHeart, FaUserPlus, FaUserMinus, FaGlobe, FaFilter } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';
import toast from 'react-hot-toast';

const Community = () => {
  const [activeTab, setActiveTab] = useState('leaderboard');
  const [leaderboard, setLeaderboard] = useState([]);
  const [communityStats, setCommunityStats] = useState(null);
  const [following, setFollowing] = useState([]);
  const [followers, setFollowers] = useState([]);
  const [activityFeed, setActivityFeed] = useState([]);
  const [loading, setLoading] = useState(true);
  const [leaderboardType, setLeaderboardType] = useState('points');
  const [leaderboardPeriod, setLeaderboardPeriod] = useState('all');
  const { user } = useAuth();
  const { t } = useLanguage();

  useEffect(() => {
    fetchCommunityData();
  }, [activeTab, leaderboardType, leaderboardPeriod]);

  const fetchCommunityData = async () => {
    try {
      setLoading(true);

      if (activeTab === 'leaderboard') {
        const [leaderboardRes, statsRes] = await Promise.all([
          axios.get(`/community/leaderboard?type=${leaderboardType}&period=${leaderboardPeriod}`),
          axios.get('/community/stats')
        ]);
        setLeaderboard(leaderboardRes.data.leaderboard);
        setCommunityStats(statsRes.data.community);
      } else if (activeTab === 'following') {
        const response = await axios.get('/community/following');
        setFollowing(response.data.following);
      } else if (activeTab === 'followers') {
        const response = await axios.get('/community/followers');
        setFollowers(response.data.followers);
      } else if (activeTab === 'feed') {
        const response = await axios.get('/community/feed');
        setActivityFeed(response.data.feed);
      }
    } catch (error) {
      console.error('Error fetching community data:', error);
      toast.error('Failed to load community data');
    } finally {
      setLoading(false);
    }
  };

  const followUser = async (userId) => {
    try {
      const response = await axios.post(`/community/follow/${userId}`);
      toast.success(response.data.message);

      // Update local state
      if (activeTab === 'leaderboard') {
        setLeaderboard(leaderboard.map(user =>
          user._id === userId
            ? { ...user, isFollowing: response.data.isFollowing }
            : user
        ));
      }

      // Refresh data if on following/followers tab
      if (activeTab === 'following' || activeTab === 'followers') {
        fetchCommunityData();
      }
    } catch (error) {
      console.error('Error following user:', error);
      toast.error(error.response?.data?.message || 'Failed to follow user');
    }
  };

  const tabs = [
    { id: 'leaderboard', label: 'Leaderboard', icon: <FaTrophy /> },
    { id: 'following', label: 'Following', icon: <FaUserPlus /> },
    { id: 'followers', label: 'Followers', icon: <FaUsers /> },
    { id: 'feed', label: 'Activity Feed', icon: <FaGlobe /> }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Community 👥</h1>
          <p className="text-gray-600">Connect with fellow eco-warriors and see how you're making a difference together</p>
        </div>

        {/* Community Stats */}
        {communityStats && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid md:grid-cols-4 gap-6 mb-8"
          >
            <div className="card text-center">
              <div className="text-3xl mb-2">👥</div>
              <div className="text-2xl font-bold text-gray-900">{communityStats.totalUsers.toLocaleString()}</div>
              <div className="text-gray-600">Total Members</div>
            </div>

            <div className="card text-center">
              <div className="text-3xl mb-2">🔥</div>
              <div className="text-2xl font-bold text-gray-900">{communityStats.activeToday.toLocaleString()}</div>
              <div className="text-gray-600">Active Today</div>
            </div>

            <div className="card text-center">
              <div className="text-3xl mb-2">✅</div>
              <div className="text-2xl font-bold text-gray-900">{communityStats.totalHabitsCompleted.toLocaleString()}</div>
              <div className="text-gray-600">Habits Completed</div>
            </div>

            <div className="card text-center">
              <div className="text-3xl mb-2">🌍</div>
              <div className="text-2xl font-bold text-gray-900">{communityStats.impact.co2Saved}kg</div>
              <div className="text-gray-600">CO₂ Saved Together</div>
            </div>
          </motion.div>
        )}

        {/* Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'bg-green-500 text-white'
                  : 'bg-white text-gray-700 hover:bg-green-50'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'leaderboard' && (
            <LeaderboardTab
              leaderboard={leaderboard}
              loading={loading}
              leaderboardType={leaderboardType}
              setLeaderboardType={setLeaderboardType}
              leaderboardPeriod={leaderboardPeriod}
              setLeaderboardPeriod={setLeaderboardPeriod}
              followUser={followUser}
              currentUserId={user?.id}
            />
          )}

          {activeTab === 'following' && (
            <FollowingTab
              following={following}
              loading={loading}
              followUser={followUser}
            />
          )}

          {activeTab === 'followers' && (
            <FollowersTab
              followers={followers}
              loading={loading}
              followUser={followUser}
            />
          )}

          {activeTab === 'feed' && (
            <ActivityFeedTab
              feed={activityFeed}
              loading={loading}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Leaderboard Tab Component
const LeaderboardTab = ({
  leaderboard,
  loading,
  leaderboardType,
  setLeaderboardType,
  leaderboardPeriod,
  setLeaderboardPeriod,
  followUser,
  currentUserId
}) => {
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex items-center space-x-2">
          <FaFilter className="text-gray-500" />
          <span className="text-gray-700 font-medium">Filter by:</span>
        </div>

        <select
          value={leaderboardType}
          onChange={(e) => setLeaderboardType(e.target.value)}
          className="input-field"
        >
          <option value="points">Points</option>
          <option value="impact">Environmental Impact</option>
          <option value="streak">Streak</option>
        </select>

        <select
          value={leaderboardPeriod}
          onChange={(e) => setLeaderboardPeriod(e.target.value)}
          className="input-field"
        >
          <option value="all">All Time</option>
          <option value="month">This Month</option>
          <option value="week">This Week</option>
        </select>
      </div>

      {/* Leaderboard */}
      <div className="card">
        <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <FaTrophy className="text-yellow-500 mr-2" />
          {leaderboardType === 'points' ? 'Points' : leaderboardType === 'impact' ? 'Environmental Impact' : 'Streak'} Leaderboard
        </h3>

        <div className="space-y-3">
          {leaderboard.map((user, index) => (
            <div
              key={user._id}
              className={`flex items-center justify-between p-4 rounded-lg transition-all ${
                user.isCurrentUser
                  ? 'bg-green-50 border-2 border-green-300'
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center space-x-4">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                  index === 0 ? 'bg-yellow-500' :
                  index === 1 ? 'bg-gray-400' :
                  index === 2 ? 'bg-orange-500' : 'bg-gray-600'
                }`}>
                  {user.rank}
                </div>

                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  {user.name.charAt(0).toUpperCase()}
                </div>

                <div>
                  <div className="font-semibold text-gray-900 flex items-center space-x-2">
                    <span>{user.name}</span>
                    {user.isCurrentUser && <span className="text-green-600">(You)</span>}
                  </div>
                  <div className="text-sm text-gray-600">
                    Level {user.ecoProfile?.level || 1} • {user.location?.city || 'Unknown location'}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  {leaderboardType === 'points' && (
                    <div className="font-bold text-gray-900">{user.ecoProfile?.totalPoints || 0} pts</div>
                  )}
                  {leaderboardType === 'impact' && (
                    <div className="font-bold text-gray-900">{user.ecoProfile?.impact?.co2Saved || 0}kg CO₂</div>
                  )}
                  {leaderboardType === 'streak' && (
                    <div className="font-bold text-gray-900 flex items-center">
                      <FaFire className="text-orange-500 mr-1" />
                      {user.ecoProfile?.streak || 0}
                    </div>
                  )}
                </div>

                {!user.isCurrentUser && (
                  <button
                    onClick={() => followUser(user._id)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      user.isFollowing
                        ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        : 'bg-green-500 text-white hover:bg-green-600'
                    }`}
                  >
                    {user.isFollowing ? 'Unfollow' : 'Follow'}
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

// Following Tab Component
const FollowingTab = ({ following, loading, followUser }) => {
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card"
    >
      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
        <FaUserPlus className="text-blue-500 mr-2" />
        Following ({following.length})
      </h3>

      {following.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">👥</div>
          <h4 className="text-lg font-semibold text-gray-900 mb-2">Not following anyone yet</h4>
          <p className="text-gray-600">Start following other eco-warriors to see their progress!</p>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {following.map(user => (
            <UserCard key={user._id} user={user} onFollow={followUser} isFollowing={true} />
          ))}
        </div>
      )}
    </motion.div>
  );
};

// Followers Tab Component
const FollowersTab = ({ followers, loading, followUser }) => {
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card"
    >
      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
        <FaUsers className="text-purple-500 mr-2" />
        Followers ({followers.length})
      </h3>

      {followers.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">👤</div>
          <h4 className="text-lg font-semibold text-gray-900 mb-2">No followers yet</h4>
          <p className="text-gray-600">Keep up your eco-friendly habits to attract followers!</p>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          {followers.map(user => (
            <UserCard key={user._id} user={user} onFollow={followUser} />
          ))}
        </div>
      )}
    </motion.div>
  );
};

// Activity Feed Tab Component
const ActivityFeedTab = ({ feed, loading }) => {
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="card"
    >
      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
        <FaGlobe className="text-green-500 mr-2" />
        Activity Feed
      </h3>

      {feed.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-4">📰</div>
          <h4 className="text-lg font-semibold text-gray-900 mb-2">No activity yet</h4>
          <p className="text-gray-600">Follow other users to see their eco-friendly activities!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {feed.map(activity => (
            <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
                {activity.user.name.charAt(0).toUpperCase()}
              </div>

              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-semibold text-gray-900">{activity.user.name}</span>
                  <span className="text-sm text-gray-500">
                    {new Date(activity.createdAt).toLocaleDateString()}
                  </span>
                </div>

                <p className="text-gray-700 mb-2">{activity.message}</p>

                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span className="flex items-center space-x-1">
                    <span>{activity.habit.icon}</span>
                    <span>{activity.habit.name}</span>
                  </span>

                  {activity.streak > 1 && (
                    <span className="flex items-center space-x-1">
                      <FaFire className="text-orange-500" />
                      <span>{activity.streak} day streak!</span>
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

// User Card Component
const UserCard = ({ user, onFollow, isFollowing = false }) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center space-x-3 mb-3">
        <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold">
          {user.name.charAt(0).toUpperCase()}
        </div>
        <div>
          <div className="font-semibold text-gray-900">{user.name}</div>
          <div className="text-sm text-gray-600">Level {user.ecoProfile?.level || 1}</div>
        </div>
      </div>

      <div className="space-y-1 text-sm text-gray-600 mb-3">
        <div>Points: {user.ecoProfile?.totalPoints || 0}</div>
        <div className="flex items-center space-x-1">
          <FaFire className="text-orange-500" />
          <span>Streak: {user.ecoProfile?.streak || 0}</span>
        </div>
      </div>

      <button
        onClick={() => onFollow(user._id)}
        className={`w-full px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          isFollowing
            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            : 'bg-green-500 text-white hover:bg-green-600'
        }`}
      >
        {isFollowing ? 'Unfollow' : 'Follow'}
      </button>
    </div>
  );
};

// Loading Spinner Component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
  </div>
);

export default Community;
