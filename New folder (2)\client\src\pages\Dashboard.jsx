import React from 'react';
import { motion } from 'framer-motion';
import { FaLeaf, FaTrophy, FaFire, FaChartLine } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Dashboard = () => {
  const { user } = useAuth();
  const { t } = useLanguage();

  const stats = [
    {
      icon: <FaTrophy className="text-yellow-500" />,
      label: 'Total Points',
      value: user?.ecoProfile?.totalPoints || 0,
      color: 'yellow'
    },
    {
      icon: <FaFire className="text-red-500" />,
      label: 'Current Streak',
      value: user?.ecoProfile?.streak || 0,
      color: 'red'
    },
    {
      icon: <FaChartLine className="text-blue-500" />,
      label: 'Level',
      value: user?.ecoProfile?.level || 1,
      color: 'blue'
    },
    {
      icon: <FaLeaf className="text-green-500" />,
      label: 'CO₂ Saved',
      value: `${user?.ecoProfile?.impact?.co2Saved || 0} kg`,
      color: 'green'
    }
  ];

  return (
    <div className="min-h-screen py-8 relative">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-green-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-purple-400/5 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-12 text-center"
        >
          <div className="inline-block mb-6">
            <span className="text-6xl floating">🌱</span>
          </div>
          <h1 className="text-5xl md:text-6xl font-black text-white mb-4">
            <span className="bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent">
              Welcome back, {user?.name}!
            </span>
          </h1>
          <p className="text-xl text-white/80 font-light">
            Ready to make a positive impact today?
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="card-eco text-center hover-lift pulse-glow group"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {stat.icon}
              </div>
              <div className="text-3xl font-black text-white mb-2 group-hover:text-green-300 transition-colors">
                {stat.value}
              </div>
              <div className="text-white/80 font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-4xl font-bold text-white text-center mb-12">
            Quick Actions
            <span className="inline-block ml-3 text-5xl floating">⚡</span>
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="card hover-lift group"
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">📋</div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors">
                Today's Habits
              </h3>
              <p className="text-white/80 mb-6 leading-relaxed">
                Track your daily eco-friendly habits
              </p>
              <button className="btn-eco w-full">
                View Habits
              </button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="card hover-lift group"
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">🌍</div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors">
                Environmental Impact
              </h3>
              <p className="text-white/80 mb-6 leading-relaxed">
                See your positive environmental impact
              </p>
              <button className="btn-secondary w-full">
                View Impact
              </button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="card hover-lift group"
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">🏆</div>
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors">
                Community Challenges
              </h3>
              <p className="text-white/80 mb-6 leading-relaxed">
                Join challenges with other eco-warriors
              </p>
              <button className="btn-secondary w-full">
                View Challenges
              </button>
            </motion.div>
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          className="card-eco"
        >
          <h3 className="text-3xl font-bold text-white mb-8 text-center">
            Recent Activity
            <span className="inline-block ml-3 text-4xl floating">📈</span>
          </h3>
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.1 }}
              className="flex items-center space-x-4 p-4 bg-green-500/20 backdrop-blur-md rounded-xl border border-green-400/30 hover-lift"
            >
              <div className="text-3xl">
                <FaLeaf className="text-green-400" />
              </div>
              <div className="flex-1">
                <p className="text-lg font-semibold text-white">
                  Completed "Use public transport"
                </p>
                <p className="text-white/70">2 hours ago</p>
              </div>
              <div className="text-green-400 font-bold">+15 pts</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="flex items-center space-x-4 p-4 bg-blue-500/20 backdrop-blur-md rounded-xl border border-blue-400/30 hover-lift"
            >
              <div className="text-3xl">
                <FaTrophy className="text-blue-400" />
              </div>
              <div className="flex-1">
                <p className="text-lg font-semibold text-white">
                  Earned 10 eco points
                </p>
                <p className="text-white/70">5 hours ago</p>
              </div>
              <div className="text-blue-400 font-bold">+10 pts</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.3 }}
              className="flex items-center space-x-4 p-4 bg-yellow-500/20 backdrop-blur-md rounded-xl border border-yellow-400/30 hover-lift"
            >
              <div className="text-3xl">
                <FaFire className="text-yellow-400" />
              </div>
              <div className="flex-1">
                <p className="text-lg font-semibold text-white">
                  Reached 7-day streak!
                </p>
                <p className="text-white/70">Yesterday</p>
              </div>
              <div className="text-yellow-400 font-bold">🔥 Streak!</div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
