import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaLeaf,
  FaRecycle,
  FaGlobe,
  FaUsers,
  FaTrophy,
  FaMapMarkerAlt,
  FaRobot,
  FaChartLine
} from 'react-icons/fa';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';

const Home = () => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <FaLeaf className="text-4xl text-green-500" />,
      title: "Daily Habit Tracker",
      description: "Track your eco-friendly habits and build sustainable routines"
    },
    {
      icon: <FaChartLine className="text-4xl text-blue-500" />,
      title: "Impact Dashboard",
      description: "See your environmental impact with CO₂, water, and energy savings"
    },
    {
      icon: <FaTrophy className="text-4xl text-yellow-500" />,
      title: "Eco Points & Rewards",
      description: "Earn points for consistency and unlock achievements"
    },
    {
      icon: <FaUsers className="text-4xl text-purple-500" />,
      title: "Community Challenges",
      description: "Join challenges and compete with other eco-warriors"
    },
    {
      icon: <FaMapMarkerAlt className="text-4xl text-red-500" />,
      title: "Eco-Friendly Places",
      description: "Find nearby recycling centers, organic shops, and EV stations"
    },
    {
      icon: <FaRobot className="text-4xl text-indigo-500" />,
      title: "AI Eco Assistant",
      description: "Get personalized eco-friendly tips and advice"
    }
  ];

  const stats = [
    { number: "10K+", label: "Active Users" },
    { number: "50K+", label: "Habits Completed" },
    { number: "2.5M", label: "kg CO₂ Saved" },
    { number: "1M+", label: "Liters Water Saved" }
  ];

  return (
    <div className="min-h-screen relative">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-green-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black/10 via-transparent to-black/10"></div>
        <div className="relative max-w-7xl mx-auto text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.8 }}
                className="inline-block"
              >
                <span className="text-8xl md:text-9xl floating">🌱</span>
              </motion.div>

              <h1 className="text-6xl md:text-8xl font-black mb-6 leading-tight">
                <span className="bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent">
                  {t('welcomeTitle')}
                </span>
              </h1>

              <p className="text-xl md:text-3xl mb-8 text-white/90 font-light max-w-4xl mx-auto leading-relaxed">
                {t('welcomeSubtitle')}
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              {!isAuthenticated ? (
                <>
                  <Link
                    to="/register"
                    className="btn-eco text-xl px-10 py-4 inline-flex items-center gap-3 group"
                  >
                    <span>{t('getStarted')}</span>
                    <span className="text-2xl group-hover:scale-110 transition-transform">🚀</span>
                  </Link>
                  <Link
                    to="/login"
                    className="btn-outline text-xl px-10 py-4 inline-flex items-center gap-3 group"
                  >
                    <span>{t('login')}</span>
                    <span className="text-2xl group-hover:scale-110 transition-transform">🔑</span>
                  </Link>
                </>
              ) : (
                <Link
                  to="/dashboard"
                  className="btn-eco text-xl px-10 py-4 inline-flex items-center gap-3 group"
                >
                  <span>Go to Dashboard</span>
                  <span className="text-2xl group-hover:scale-110 transition-transform">📊</span>
                </Link>
              )}
            </motion.div>
          </motion.div>
        </div>

        {/* Floating elements */}
        <div className="absolute top-20 left-10 animate-float">
          <FaLeaf className="text-6xl text-green-300/30" />
        </div>
        <div className="absolute bottom-20 right-10 animate-float" style={{ animationDelay: '1s' }}>
          <FaRecycle className="text-6xl text-blue-300/30" />
        </div>
        <div className="absolute top-1/2 left-20 animate-float" style={{ animationDelay: '2s' }}>
          <FaGlobe className="text-5xl text-yellow-300/30" />
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 relative">
        <div className="max-w-6xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-5xl font-bold text-white mb-4">
              Our Global Impact 🌍
            </h2>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Together, we're making a real difference for our planet
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card text-center hover-glow pulse-glow"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="text-4xl md:text-5xl font-black text-green-400 mb-3">
                  {stat.number}
                </div>
                <div className="text-white/90 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 relative">
        <div className="max-w-7xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <h2 className="text-6xl font-black text-white mb-6">
              Why Choose GreenMate?
              <span className="inline-block ml-4 text-7xl floating">🌍</span>
            </h2>
            <p className="text-2xl text-white/80 max-w-4xl mx-auto leading-relaxed">
              Join thousands of eco-warriors making a positive impact on our planet through daily sustainable habits.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, rotateY: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="card-eco text-center hover-lift group"
              >
                <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-white/80 leading-relaxed text-lg">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/20 via-emerald-500/20 to-teal-600/20"></div>
        <div className="max-w-6xl mx-auto text-center px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-7xl font-black text-white mb-6 leading-tight">
                Ready to Make a Difference?
                <span className="inline-block ml-4 text-8xl floating">🌟</span>
              </h2>
              <p className="text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed font-light">
                Start your eco-friendly journey today and be part of the solution for a sustainable future.
              </p>
            </div>

            {!isAuthenticated && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5, duration: 0.8 }}
              >
                <Link
                  to="/register"
                  className="btn-eco text-2xl px-12 py-6 inline-flex items-center gap-4 group shadow-2xl"
                >
                  <span>Join GreenMate Now!</span>
                  <span className="text-3xl group-hover:scale-125 transition-transform">🚀</span>
                </Link>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Floating particles */}
        <div className="absolute top-10 left-10 w-4 h-4 bg-green-400/30 rounded-full floating"></div>
        <div className="absolute top-1/3 right-20 w-6 h-6 bg-blue-400/30 rounded-full floating" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-1/4 w-3 h-3 bg-yellow-400/30 rounded-full floating" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-1/3 right-1/3 w-5 h-5 bg-purple-400/30 rounded-full floating" style={{ animationDelay: '3s' }}></div>
      </section>
    </div>
  );
};

export default Home;
