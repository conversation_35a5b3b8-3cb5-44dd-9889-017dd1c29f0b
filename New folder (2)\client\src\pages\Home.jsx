import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaLeaf, 
  FaRecycle, 
  FaGlobe, 
  FaUsers, 
  FaTrophy, 
  FaMapMarkerAlt,
  FaRobot,
  FaChartLine
} from 'react-icons/fa';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';

const Home = () => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <FaLeaf className="text-4xl text-green-500" />,
      title: "Daily Habit Tracker",
      description: "Track your eco-friendly habits and build sustainable routines"
    },
    {
      icon: <FaChartLine className="text-4xl text-blue-500" />,
      title: "Impact Dashboard",
      description: "See your environmental impact with CO₂, water, and energy savings"
    },
    {
      icon: <FaTrophy className="text-4xl text-yellow-500" />,
      title: "Eco Points & Rewards",
      description: "Earn points for consistency and unlock achievements"
    },
    {
      icon: <FaUsers className="text-4xl text-purple-500" />,
      title: "Community Challenges",
      description: "Join challenges and compete with other eco-warriors"
    },
    {
      icon: <FaMapMarkerAlt className="text-4xl text-red-500" />,
      title: "Eco-Friendly Places",
      description: "Find nearby recycling centers, organic shops, and EV stations"
    },
    {
      icon: <FaRobot className="text-4xl text-indigo-500" />,
      title: "AI Eco Assistant",
      description: "Get personalized eco-friendly tips and advice"
    }
  ];

  const stats = [
    { number: "10K+", label: "Active Users" },
    { number: "50K+", label: "Habits Completed" },
    { number: "2.5M", label: "kg CO₂ Saved" },
    { number: "1M+", label: "Liters Water Saved" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-nature text-white py-20 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              🌱 {t('welcomeTitle')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              {t('welcomeSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {!isAuthenticated ? (
                <>
                  <Link
                    to="/register"
                    className="btn-eco text-lg px-8 py-3 inline-block"
                  >
                    {t('getStarted')} 🚀
                  </Link>
                  <Link
                    to="/login"
                    className="bg-white/20 hover:bg-white/30 text-white font-medium py-3 px-8 rounded-lg transition-all duration-200 backdrop-blur-sm"
                  >
                    {t('login')}
                  </Link>
                </>
              ) : (
                <Link
                  to="/dashboard"
                  className="btn-eco text-lg px-8 py-3 inline-block"
                >
                  Go to Dashboard 📊
                </Link>
              )}
            </div>
          </motion.div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute top-20 left-10 animate-float">
          <FaLeaf className="text-6xl text-green-300/30" />
        </div>
        <div className="absolute bottom-20 right-10 animate-float" style={{ animationDelay: '1s' }}>
          <FaRecycle className="text-6xl text-blue-300/30" />
        </div>
        <div className="absolute top-1/2 left-20 animate-float" style={{ animationDelay: '2s' }}>
          <FaGlobe className="text-5xl text-yellow-300/30" />
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose GreenMate? 🌍
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join thousands of eco-warriors making a positive impact on our planet through daily sustainable habits.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="card text-center hover:shadow-eco-lg transition-all duration-300"
              >
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-eco text-white">
        <div className="max-w-4xl mx-auto text-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold mb-6">
              Ready to Make a Difference? 🌟
            </h2>
            <p className="text-xl mb-8 text-green-100">
              Start your eco-friendly journey today and be part of the solution for a sustainable future.
            </p>
            {!isAuthenticated && (
              <Link
                to="/register"
                className="bg-white text-green-600 hover:bg-green-50 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-200 inline-block transform hover:scale-105"
              >
                Join GreenMate Now! 🚀
              </Link>
            )}
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
