import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaLeaf, FaTint, FaBolt, FaRecycle, FaTree, FaCalendarAlt, FaTrophy, FaShare } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';
import toast from 'react-hot-toast';

const Impact = () => {
  const [impactData, setImpactData] = useState(null);
  const [timeframe, setTimeframe] = useState('all');
  const [loading, setLoading] = useState(true);
  const [calendarData, setCalendarData] = useState({});
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const { user } = useAuth();
  const { t } = useLanguage();

  useEffect(() => {
    fetchImpactData();
    fetchCalendarData();
  }, [timeframe, selectedMonth, selectedYear]);

  const fetchImpactData = async () => {
    try {
      setLoading(true);
      // Get user's current impact from profile
      const userImpact = user?.ecoProfile?.impact || {
        co2Saved: 0,
        waterSaved: 0,
        electricitySaved: 0,
        plasticAvoided: 0,
        treesEquivalent: 0
      };

      // Calculate additional metrics
      const additionalMetrics = {
        carbonFootprintReduction: Math.round(userImpact.co2Saved * 2.2), // lbs
        waterBottlesSaved: Math.round(userImpact.plasticAvoided / 25), // assuming 25g per bottle
        energyBillSavings: Math.round(userImpact.electricitySaved * 0.12), // $0.12 per kWh
        equivalentMiles: Math.round(userImpact.co2Saved * 2.5), // miles not driven
      };

      setImpactData({
        ...userImpact,
        ...additionalMetrics
      });
    } catch (error) {
      console.error('Error fetching impact data:', error);
      toast.error('Failed to load impact data');
    } finally {
      setLoading(false);
    }
  };

  const fetchCalendarData = async () => {
    try {
      const response = await axios.get(`/tracking/calendar/${selectedYear}/${selectedMonth}`);
      setCalendarData(response.data.calendar);
    } catch (error) {
      console.error('Error fetching calendar data:', error);
    }
  };

  const shareImpact = async () => {
    const shareText = `🌱 My GreenMate Impact:\n🌍 ${impactData.co2Saved}kg CO₂ saved\n💧 ${impactData.waterSaved}L water conserved\n⚡ ${impactData.electricitySaved}kWh energy saved\n♻️ ${impactData.plasticAvoided}g plastic avoided\n\nJoin me in making a difference! #GreenMate #EcoFriendly`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'My Environmental Impact',
          text: shareText,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(shareText);
      toast.success('Impact stats copied to clipboard!');
    }
  };

  const impactCards = [
    {
      title: 'CO₂ Saved',
      value: impactData?.co2Saved || 0,
      unit: 'kg',
      icon: <FaLeaf className="text-green-500" />,
      description: `Equivalent to ${impactData?.equivalentMiles || 0} miles not driven`,
      color: 'green',
      comparison: 'A tree absorbs ~22kg CO₂ per year'
    },
    {
      title: 'Water Saved',
      value: impactData?.waterSaved || 0,
      unit: 'liters',
      icon: <FaTint className="text-blue-500" />,
      description: `That's ${Math.round((impactData?.waterSaved || 0) / 8)} days of drinking water`,
      color: 'blue',
      comparison: 'Average person uses 300L per day'
    },
    {
      title: 'Energy Saved',
      value: impactData?.electricitySaved || 0,
      unit: 'kWh',
      icon: <FaBolt className="text-yellow-500" />,
      description: `Saved ~$${impactData?.energyBillSavings || 0} on energy bills`,
      color: 'yellow',
      comparison: 'Powers a home for ~2 hours'
    },
    {
      title: 'Plastic Avoided',
      value: impactData?.plasticAvoided || 0,
      unit: 'grams',
      icon: <FaRecycle className="text-purple-500" />,
      description: `${impactData?.waterBottlesSaved || 0} plastic bottles avoided`,
      color: 'purple',
      comparison: 'Plastic takes 450+ years to decompose'
    }
  ];

  const achievements = [
    {
      title: 'Eco Warrior',
      description: 'Saved 10kg+ CO₂',
      icon: '🌟',
      achieved: (impactData?.co2Saved || 0) >= 10,
      progress: Math.min(100, ((impactData?.co2Saved || 0) / 10) * 100)
    },
    {
      title: 'Water Guardian',
      description: 'Saved 100L+ water',
      icon: '💧',
      achieved: (impactData?.waterSaved || 0) >= 100,
      progress: Math.min(100, ((impactData?.waterSaved || 0) / 100) * 100)
    },
    {
      title: 'Energy Saver',
      description: 'Saved 50kWh+ energy',
      icon: '⚡',
      achieved: (impactData?.electricitySaved || 0) >= 50,
      progress: Math.min(100, ((impactData?.electricitySaved || 0) / 50) * 100)
    },
    {
      title: 'Plastic Fighter',
      description: 'Avoided 1kg+ plastic',
      icon: '♻️',
      achieved: (impactData?.plasticAvoided || 0) >= 1000,
      progress: Math.min(100, ((impactData?.plasticAvoided || 0) / 1000) * 100)
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Environmental Impact 📊</h1>
            <p className="text-gray-600">See the positive difference you're making for our planet</p>
          </div>
          <button
            onClick={shareImpact}
            className="btn-eco flex items-center space-x-2"
          >
            <FaShare />
            <span>Share Impact</span>
          </button>
        </div>

        {/* Summary Stats */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {impactCards.map((card, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="card-eco"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="text-3xl">{card.icon}</div>
                <div className={`badge badge-${card.color === 'green' ? 'success' : card.color === 'blue' ? 'info' : 'warning'}`}>
                  {card.title}
                </div>
              </div>

              <div className="text-3xl font-bold text-gray-900 mb-1">
                {card.value.toLocaleString()}
                <span className="text-lg text-gray-600 ml-1">{card.unit}</span>
              </div>

              <p className="text-sm text-gray-600 mb-2">{card.description}</p>
              <p className="text-xs text-gray-500">{card.comparison}</p>
            </motion.div>
          ))}
        </div>

        {/* Trees Equivalent */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="card-eco mb-8 text-center"
        >
          <FaTree className="text-6xl text-green-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Your impact equals {Math.round((impactData?.co2Saved || 0) / 22 * 10) / 10} trees planted! 🌳
          </h3>
          <p className="text-gray-600">
            Based on CO₂ absorption, your eco-friendly habits have the same impact as planting trees
          </p>
        </motion.div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="card mb-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <FaTrophy className="text-yellow-500 mr-2" />
            Impact Achievements
          </h3>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {achievements.map((achievement, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 transition-all ${
                  achievement.achieved
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="text-center">
                  <div className="text-3xl mb-2">{achievement.icon}</div>
                  <h4 className="font-semibold text-gray-900 mb-1">{achievement.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{achievement.description}</p>

                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        achievement.achieved ? 'bg-green-500' : 'bg-blue-500'
                      }`}
                      style={{ width: `${achievement.progress}%` }}
                    ></div>
                  </div>

                  <div className="text-xs text-gray-500 mt-1">
                    {Math.round(achievement.progress)}% complete
                  </div>

                  {achievement.achieved && (
                    <div className="text-green-600 font-medium text-sm mt-2">
                      ✅ Achieved!
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Activity Calendar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="card"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 flex items-center">
              <FaCalendarAlt className="text-blue-500 mr-2" />
              Activity Calendar
            </h3>

            <div className="flex items-center space-x-4">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="input-field"
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {new Date(2024, i).toLocaleString('default', { month: 'long' })}
                  </option>
                ))}
              </select>

              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="input-field"
              >
                {Array.from({ length: 3 }, (_, i) => (
                  <option key={2024 - i} value={2024 - i}>
                    {2024 - i}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <ActivityCalendar
            calendarData={calendarData}
            month={selectedMonth}
            year={selectedYear}
          />
        </motion.div>

        {/* Impact Comparison */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
          className="card"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6">Global Impact Comparison</h3>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Your Contribution</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>CO₂ Reduction</span>
                  <span className="font-medium">{impactData?.co2Saved || 0}kg</span>
                </div>
                <div className="flex justify-between">
                  <span>Water Conservation</span>
                  <span className="font-medium">{impactData?.waterSaved || 0}L</span>
                </div>
                <div className="flex justify-between">
                  <span>Energy Savings</span>
                  <span className="font-medium">{impactData?.electricitySaved || 0}kWh</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Real-World Equivalents</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div>🚗 {Math.round((impactData?.co2Saved || 0) * 2.5)} miles of driving avoided</div>
                <div>🏠 {Math.round((impactData?.electricitySaved || 0) / 30)} days of home electricity</div>
                <div>🚿 {Math.round((impactData?.waterSaved || 0) / 10)} long showers saved</div>
                <div>🌳 {Math.round((impactData?.co2Saved || 0) / 22 * 10) / 10} trees worth of CO₂ absorption</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

// Activity Calendar Component
const ActivityCalendar = ({ calendarData, month, year }) => {
  const getDaysInMonth = (month, year) => {
    return new Date(year, month, 0).getDate();
  };

  const getFirstDayOfMonth = (month, year) => {
    return new Date(year, month - 1, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(month, year);
  const firstDay = getFirstDayOfMonth(month, year);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const emptyDays = Array.from({ length: firstDay }, (_, i) => null);

  const getActivityLevel = (day) => {
    const dateKey = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    const activities = calendarData[dateKey] || [];

    if (activities.length === 0) return 'none';
    if (activities.length <= 2) return 'low';
    if (activities.length <= 4) return 'medium';
    return 'high';
  };

  const getActivityColor = (level) => {
    switch (level) {
      case 'none': return 'bg-gray-100';
      case 'low': return 'bg-green-200';
      case 'medium': return 'bg-green-400';
      case 'high': return 'bg-green-600';
      default: return 'bg-gray-100';
    }
  };

  return (
    <div>
      <div className="grid grid-cols-7 gap-1 mb-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-sm font-medium text-gray-600 p-2">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1">
        {emptyDays.map((_, index) => (
          <div key={`empty-${index}`} className="h-8"></div>
        ))}

        {days.map(day => {
          const level = getActivityLevel(day);
          const dateKey = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
          const activities = calendarData[dateKey] || [];

          return (
            <div
              key={day}
              className={`h-8 rounded flex items-center justify-center text-sm font-medium cursor-pointer transition-all hover:scale-110 ${getActivityColor(level)} ${
                level === 'none' ? 'text-gray-600' : 'text-white'
              }`}
              title={`${day}/${month}/${year}: ${activities.length} habits completed`}
            >
              {day}
            </div>
          );
        })}
      </div>

      <div className="flex items-center justify-center space-x-4 mt-4 text-sm text-gray-600">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-gray-100 rounded"></div>
          <span>No activity</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-green-200 rounded"></div>
          <span>Low</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-green-400 rounded"></div>
          <span>Medium</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-green-600 rounded"></div>
          <span>High</span>
        </div>
      </div>
    </div>
  );
};

export default Impact;
