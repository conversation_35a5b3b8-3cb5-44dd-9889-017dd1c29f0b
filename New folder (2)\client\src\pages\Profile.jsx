import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const Profile = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);

  // Helper function to format location
  const formatLocation = (location) => {
    if (!location) return 'Earth 🌍';
    if (typeof location === 'string') return location;
    if (typeof location === 'object') {
      if (location.city && location.country) {
        return `${location.city}, ${location.country}`;
      }
      if (location.city) return location.city;
      if (location.country) return location.country;
    }
    return 'Earth 🌍';
  };

  // Use actual user data with fallbacks
  const profileData = {
    name: user?.name || 'Guest User',
    email: user?.email || '<EMAIL>',
    bio: user?.bio || 'Welcome to GreenMate! Start your eco-friendly journey today.',
    location: formatLocation(user?.location),
    avatar: user?.avatar || '🌱',
    joinDate: user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Recently',
    level: user?.ecoProfile?.level || 1,
    totalPoints: user?.ecoProfile?.totalPoints || 0,
    streak: user?.ecoProfile?.streak || 0,
    impact: user?.ecoProfile?.impact || {
      co2Saved: 0,
      waterSaved: 0,
      electricitySaved: 0,
      plasticAvoided: 0,
      treesEquivalent: 0
    },
    social: user?.social || {
      followingCount: 0,
      followersCount: 0,
      friendsCount: 0
    }
  };

  const [editForm, setEditForm] = useState({
    name: profileData.name,
    bio: profileData.bio,
    location: profileData.location
  });

  // Simple eco stats based on user data
  const ecoStats = [
    {
      label: 'CO₂ Saved',
      value: `${profileData.impact.co2Saved || 0} kg`,
      icon: '🌱',
      color: 'text-green-600',
      bg: 'bg-green-100'
    },
    {
      label: 'Water Saved',
      value: `${profileData.impact.waterSaved || 0}L`,
      icon: '💧',
      color: 'text-blue-600',
      bg: 'bg-blue-100'
    },
    {
      label: 'Energy Saved',
      value: `${profileData.impact.electricitySaved || 0} kWh`,
      icon: '⚡',
      color: 'text-yellow-600',
      bg: 'bg-yellow-100'
    },
    {
      label: 'Plastic Avoided',
      value: `${profileData.impact.plasticAvoided || 0}g`,
      icon: '♻️',
      color: 'text-purple-600',
      bg: 'bg-purple-100'
    }
  ];

  const handleSaveProfile = () => {
    // In a real app, this would update the user profile via API
    toast.success('Profile updated successfully! 🌱');
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Simple Profile Header */}
        <div className="card mb-8">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            {/* Avatar */}
            <div className="relative">
              <div className="avatar avatar-xl eco-shadow hover-scale">
                {profileData.avatar}
              </div>
              <div className="absolute -top-1 -right-1 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg">
                {profileData.level}
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-4">
                  <input
                    type="text"
                    value={editForm.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="input-eco text-2xl font-bold"
                    placeholder="Your name"
                  />
                  <textarea
                    value={editForm.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    className="input-eco"
                    rows="2"
                    placeholder="Tell us about yourself..."
                  />
                  <input
                    type="text"
                    value={editForm.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className="input-eco"
                    placeholder="Your location"
                  />
                  <div className="flex gap-2">
                    <button onClick={handleSaveProfile} className="btn-eco">
                      💾 Save
                    </button>
                    <button onClick={() => setIsEditing(false)} className="btn-secondary">
                      ❌ Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex items-center gap-4 mb-2">
                    <h1 className="text-2xl font-bold text-gray-900">{profileData.name}</h1>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="text-gray-500 hover:text-green-600 transition-colors"
                    >
                      ✏️
                    </button>
                  </div>
                  <p className="text-gray-600 mb-3">{profileData.bio}</p>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                    <span>📧 {profileData.email}</span>
                    <span>📍 {profileData.location}</span>
                    <span>📅 Joined {profileData.joinDate}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-xl font-bold text-green-600">{profileData.totalPoints}</div>
                <div className="text-xs text-gray-600">Points</div>
              </div>
              <div>
                <div className="text-xl font-bold text-orange-600">{profileData.streak}</div>
                <div className="text-xs text-gray-600">Streak</div>
              </div>
              <div>
                <div className="text-xl font-bold text-blue-600">{profileData.social.friendsCount}</div>
                <div className="text-xs text-gray-600">Friends</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Eco Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {ecoStats.map((stat, index) => (
            <div key={stat.label} className="stat-card hover-lift eco-glow">
              <div className={`w-12 h-12 ${stat.bg} rounded-xl flex items-center justify-center mx-auto mb-3 hover-scale`}>
                <span className="text-xl">{stat.icon}</span>
              </div>
              <div className={`stat-value ${stat.color}`}>{stat.value}</div>
              <div className="stat-label">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Enhanced Tabs */}
        <div className="flex justify-center mb-8">
          <div className="tab-nav">
            {[
              { id: 'profile', label: 'Profile', icon: '👤' },
              { id: 'impact', label: 'Impact', icon: '🌱' },
              { id: 'activity', label: 'Activity', icon: '📊' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`tab-button ${
                  activeTab === tab.id ? 'tab-button-active' : 'tab-button-inactive'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="card">
          {activeTab === 'profile' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <div className="text-gray-900">{profileData.name}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div className="text-gray-900">{profileData.email}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <div className="text-gray-900">{profileData.location}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                    <div className="text-gray-900">{profileData.joinDate}</div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                  <div className="text-gray-900">{profileData.bio}</div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'impact' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-6 eco-text-gradient">Environmental Impact</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200 hover-lift">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center text-white text-xl hover-scale">
                        🌱
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">CO₂ Saved</div>
                        <div className="text-2xl font-bold text-green-600">{profileData.impact.co2Saved} kg</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">Equivalent to planting trees 🌳</div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl border border-blue-200 hover-lift">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center text-white text-xl hover-scale">
                        💧
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Water Saved</div>
                        <div className="text-2xl font-bold text-blue-600">{profileData.impact.waterSaved}L</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">Enough for daily needs 🚿</div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="p-6 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl border border-yellow-200 hover-lift">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center text-white text-xl hover-scale">
                        ⚡
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Energy Saved</div>
                        <div className="text-2xl font-bold text-yellow-600">{profileData.impact.electricitySaved} kWh</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">Renewable energy usage ☀️</div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200 hover-lift">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center text-white text-xl hover-scale">
                        ♻️
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Plastic Avoided</div>
                        <div className="text-2xl font-bold text-purple-600">{profileData.impact.plasticAvoided}g</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">Waste reduction efforts 🌍</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                  <span className="text-2xl">🌱</span>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Joined GreenMate</div>
                    <div className="text-sm text-gray-600">Started your eco journey</div>
                  </div>
                  <div className="text-sm text-gray-500">{profileData.joinDate}</div>
                </div>
                <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                  <span className="text-2xl">📊</span>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">Current Level</div>
                    <div className="text-sm text-gray-600">Level {profileData.level} with {profileData.totalPoints} points</div>
                  </div>
                  <div className="text-sm text-gray-500">Current</div>
                </div>
                {profileData.streak > 0 && (
                  <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                    <span className="text-2xl">🔥</span>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">Streak Active</div>
                      <div className="text-sm text-gray-600">{profileData.streak} days of consistent eco actions</div>
                    </div>
                    <div className="text-sm text-gray-500">Active</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
