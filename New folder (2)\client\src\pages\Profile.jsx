import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import toast from 'react-hot-toast';

const Profile = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState(null);

  const [profileData, setProfileData] = useState({
    name: user?.name || 'Eco Warrior',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Passionate about sustainable living and environmental conservation. Making small changes for a big impact! 🌱',
    website: 'https://eco-warrior.com',
    joinDate: 'January 2024',
    avatar: '🌱',
    level: 12,
    xp: 2340,
    nextLevelXp: 3000,
    title: 'Eco Enthusiast',
    badges: ['🌱', '♻️', '💧', '⚡', '🌍', '🌿', '🌳'],
    socialLinks: {
      twitter: '@ecowarrior',
      instagram: '@green_lifestyle',
      linkedin: 'eco-warrior'
    },
    preferences: {
      notifications: true,
      publicProfile: true,
      shareProgress: true,
      showEmail: false,
      showPhone: false,
      showLocation: true
    },
    stats: {
      totalPoints: 2847,
      streakDays: 23,
      habitsCompleted: 156,
      challengesWon: 12,
      friendsCount: 47,
      postsCount: 89
    }
  });

  const [editForm, setEditForm] = useState({...profileData});

  const avatarOptions = [
    '🌱', '🌿', '🌳', '🌍', '♻️', '💧', '⚡', '🌞', '🌙', '⭐',
    '🦋', '🐝', '🌺', '🌻', '🌸', '🍃', '🌾', '🌵', '🌴', '🌲',
    '🐢', '🦎', '🐸', '🦜', '🦅', '🐋', '🐠', '🌊', '🏔️', '🌋'
  ];

  const ecoStats = [
    {
      label: 'CO₂ Saved',
      value: '2.5 tons',
      icon: '🌱',
      color: 'text-green-600',
      bg: 'bg-green-100',
      trend: '+12%',
      description: 'Carbon footprint reduction this year',
      details: 'Equivalent to planting 50 trees'
    },
    {
      label: 'Items Recycled',
      value: '1,247',
      icon: '♻️',
      color: 'text-blue-600',
      bg: 'bg-blue-100',
      trend: '+8%',
      description: 'Waste diverted from landfills',
      details: 'Saved 15.6 cubic meters of landfill space'
    },
    {
      label: 'Water Saved',
      value: '850L',
      icon: '💧',
      color: 'text-cyan-600',
      bg: 'bg-cyan-100',
      trend: '+15%',
      description: 'Water conservation efforts',
      details: 'Enough for 30 days of drinking water'
    },
    {
      label: 'Energy Saved',
      value: '340 kWh',
      icon: '⚡',
      color: 'text-yellow-600',
      bg: 'bg-yellow-100',
      trend: '+5%',
      description: 'Renewable energy usage',
      details: 'Powers an average home for 2 weeks'
    }
  ];

  const achievements = [
    {
      icon: '🌱',
      title: 'Green Beginner',
      description: 'Started your eco journey',
      date: 'Jan 2024',
      completed: true,
      rarity: 'common',
      points: 50,
      progress: 100
    },
    {
      icon: '🔥',
      title: '7-Day Streak',
      description: 'Maintained habits for a week',
      date: 'Feb 2024',
      completed: true,
      rarity: 'uncommon',
      points: 100,
      progress: 100
    },
    {
      icon: '♻️',
      title: 'Recycling Champion',
      description: 'Recycled 1000+ items',
      date: 'Mar 2024',
      completed: true,
      rarity: 'rare',
      points: 250,
      progress: 100
    },
    {
      icon: '🌍',
      title: 'Planet Protector',
      description: 'Saved 2+ tons of CO₂',
      date: 'In Progress',
      completed: false,
      rarity: 'epic',
      points: 500,
      progress: 85
    },
    {
      icon: '🏆',
      title: 'Eco Master',
      description: 'Complete 100 eco challenges',
      date: 'Locked',
      completed: false,
      rarity: 'legendary',
      points: 1000,
      progress: 12
    },
    {
      icon: '🌟',
      title: 'Community Leader',
      description: 'Help 50 people start their eco journey',
      date: 'In Progress',
      completed: false,
      rarity: 'epic',
      points: 750,
      progress: 60
    }
  ];

  const recentActivities = [
    {
      icon: '🌱',
      action: 'Completed daily eco-challenge',
      time: '2 hours ago',
      points: '+15 pts',
      type: 'success',
      category: 'Challenge',
      details: 'Used reusable water bottle for the entire day'
    },
    {
      icon: '♻️',
      action: 'Recycled plastic bottles',
      time: '5 hours ago',
      points: '+10 pts',
      type: 'info',
      category: 'Recycling',
      details: 'Properly sorted 5 plastic bottles'
    },
    {
      icon: '🌿',
      action: 'Planted virtual tree',
      time: '1 day ago',
      points: '+25 pts',
      type: 'success',
      category: 'Conservation',
      details: 'Contributed to reforestation project'
    },
    {
      icon: '💧',
      action: 'Water conservation goal met',
      time: '2 days ago',
      points: '+20 pts',
      type: 'info',
      category: 'Conservation',
      details: 'Saved 50L of water through efficient usage'
    },
    {
      icon: '⚡',
      action: 'Used renewable energy',
      time: '3 days ago',
      points: '+30 pts',
      type: 'success',
      category: 'Energy',
      details: 'Switched to solar power for home'
    },
    {
      icon: '🚲',
      action: 'Biked to work',
      time: '4 days ago',
      points: '+12 pts',
      type: 'success',
      category: 'Transport',
      details: 'Avoided 5kg CO₂ emissions'
    }
  ];

  const handleSaveProfile = () => {
    setProfileData({...editForm});
    setIsEditing(false);
    toast.success('Profile updated successfully! 🌱');
  };

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setEditForm({...editForm, avatar});
    setShowAvatarModal(false);
    toast.success('Avatar updated! ✨');
  };

  const handleShareProfile = () => {
    navigator.clipboard.writeText(`Check out my GreenMate profile! I've saved ${ecoStats[0].value} of CO₂ and completed ${profileData.stats.habitsCompleted} eco-friendly habits! 🌱`);
    toast.success('Profile link copied to clipboard! 📋');
    setShowShareModal(false);
  };

  const getRarityColor = (rarity) => {
    const colors = {
      common: 'border-gray-300 bg-gray-50',
      uncommon: 'border-green-400 bg-green-50',
      rare: 'border-blue-400 bg-blue-50',
      epic: 'border-purple-400 bg-purple-50',
      legendary: 'border-yellow-400 bg-yellow-50'
    };
    return colors[rarity] || 'border-gray-300 bg-gray-50';
  };

  const getRarityBadgeColor = (rarity) => {
    const colors = {
      common: 'bg-gray-100 text-gray-800',
      uncommon: 'bg-green-100 text-green-800',
      rare: 'bg-blue-100 text-blue-800',
      epic: 'bg-purple-100 text-purple-800',
      legendary: 'bg-yellow-100 text-yellow-800'
    };
    return colors[rarity] || 'bg-gray-100 text-gray-800';
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    if (progress >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="card-eco mb-8 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 text-6xl text-green-300">🌱</div>
            <div className="absolute top-20 right-20 text-4xl text-blue-300">💧</div>
            <div className="absolute bottom-10 left-20 text-5xl text-yellow-300">⚡</div>
            <div className="absolute bottom-20 right-10 text-4xl text-purple-300">♻️</div>
          </div>

          <div className="relative z-10 flex flex-col lg:flex-row items-start lg:items-center gap-8">
            {/* Avatar Section */}
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-eco rounded-full flex items-center justify-center text-white text-5xl font-bold shadow-xl border-4 border-white">
                {profileData.avatar}
              </div>
              <button
                onClick={() => setShowAvatarModal(true)}
                className="absolute bottom-2 right-2 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-600 hover:text-green-600 hover:shadow-xl transition-all"
              >
                📷
              </button>

              {/* Level Badge */}
              <div className="absolute -top-2 -right-2 w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                {profileData.level}
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-1">{profileData.name}</h1>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="px-3 py-1 bg-gradient-eco text-white rounded-full text-sm font-medium">
                      {profileData.title}
                    </span>
                    <div className="flex gap-1">
                      {profileData.badges.slice(0, 5).map((badge, index) => (
                        <span key={index} className="text-lg">{badge}</span>
                      ))}
                      {profileData.badges.length > 5 && (
                        <span className="text-sm text-gray-500">+{profileData.badges.length - 5}</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setIsEditing(!isEditing)}
                    className="btn-secondary px-4 py-2 text-sm flex items-center gap-2"
                  >
                    {isEditing ? '❌ Cancel' : '✏️ Edit Profile'}
                  </button>
                  <button
                    onClick={() => setShowShareModal(true)}
                    className="btn-eco px-4 py-2 text-sm flex items-center gap-2"
                  >
                    📤 Share
                  </button>
                </div>
              </div>

              <p className="text-gray-600 mb-4 max-w-2xl">{profileData.bio}</p>

              {/* Contact Info */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2 text-gray-500">
                  <span>📧</span>
                  <span className={profileData.preferences.showEmail ? '' : 'blur-sm'}>
                    {profileData.email}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                  <span>📍</span>
                  <span>{profileData.location}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                  <span>📅</span>
                  <span>Joined {profileData.joinDate}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                  <span>🌐</span>
                  <a href={profileData.website} className="text-green-600 hover:text-green-700">
                    Website
                  </a>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex gap-4 mt-4">
                {profileData.socialLinks.twitter && (
                  <a href={`https://twitter.com/${profileData.socialLinks.twitter.replace('@', '')}`}
                     className="text-blue-500 hover:text-blue-600">
                    🐦 Twitter
                  </a>
                )}
                {profileData.socialLinks.instagram && (
                  <a href={`https://instagram.com/${profileData.socialLinks.instagram.replace('@', '')}`}
                     className="text-pink-500 hover:text-pink-600">
                    📷 Instagram
                  </a>
                )}
                {profileData.socialLinks.linkedin && (
                  <a href={`https://linkedin.com/in/${profileData.socialLinks.linkedin}`}
                     className="text-blue-700 hover:text-blue-800">
                    💼 LinkedIn
                  </a>
                )}
              </div>
            </div>

            {/* Stats Summary */}
            <div className="grid grid-cols-2 gap-4 lg:grid-cols-1 lg:gap-2">
              <div className="text-center p-4 bg-white bg-opacity-70 rounded-xl">
                <div className="text-2xl font-bold text-green-600">{profileData.stats.totalPoints}</div>
                <div className="text-xs text-gray-600">Total Points</div>
              </div>
              <div className="text-center p-4 bg-white bg-opacity-70 rounded-xl">
                <div className="text-2xl font-bold text-orange-600">{profileData.stats.streakDays}</div>
                <div className="text-xs text-gray-600">Day Streak</div>
              </div>
              <div className="text-center p-4 bg-white bg-opacity-70 rounded-xl">
                <div className="text-2xl font-bold text-blue-600">{profileData.stats.friendsCount}</div>
                <div className="text-xs text-gray-600">Friends</div>
              </div>
              <div className="text-center p-4 bg-white bg-opacity-70 rounded-xl">
                <div className="text-2xl font-bold text-purple-600">{profileData.stats.challengesWon}</div>
                <div className="text-xs text-gray-600">Challenges</div>
              </div>
            </div>
          </div>

          {/* XP Progress Bar */}
          <div className="mt-6 relative z-10">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Level {profileData.level} Progress</span>
              <span>{profileData.xp} / {profileData.nextLevelXp} XP</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${(profileData.xp / profileData.nextLevelXp) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Eco Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          {ecoStats.map((stat, index) => (
            <div key={stat.label} className="card-eco text-center hover-lift group cursor-pointer">
              <div className={`w-16 h-16 ${stat.bg} rounded-xl flex items-center justify-center mx-auto mb-4 relative group-hover:scale-110 transition-transform`}>
                <span className="text-2xl">{stat.icon}</span>
                <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                  {stat.trend}
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
              <div className="text-sm font-medium text-gray-700 mb-2">{stat.label}</div>
              <div className="text-xs text-gray-500 mb-2">{stat.description}</div>
              <div className="text-xs text-green-600 font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                {stat.details}
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-white rounded-xl p-1 shadow-lg border border-gray-200">
            {[
              { id: 'overview', label: 'Overview', icon: '👤' },
              { id: 'achievements', label: 'Achievements', icon: '🏆' },
              { id: 'activity', label: 'Activity', icon: '🔥' },
              { id: 'analytics', label: 'Analytics', icon: '📊' },
              { id: 'social', label: 'Social', icon: '👥' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-eco text-white shadow-md transform scale-105'
                    : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                }`}
              >
                <span>{tab.icon}</span>
                <span className="hidden sm:block">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Environmental Impact */}
              <div className="card-eco">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">🌍</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Environmental Impact</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                    <span className="text-gray-700">This Month</span>
                    <span className="font-bold text-green-600">+15% improvement</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    <span className="text-gray-700">This Year</span>
                    <span className="font-bold text-blue-600">+32% improvement</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                    <span className="text-gray-700">All Time</span>
                    <span className="font-bold text-purple-600">Top 5% globally</span>
                  </div>
                  <div className="p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 mb-1">Carbon Neutral</div>
                      <div className="text-sm text-gray-600">Projected by Dec 2024</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="card-eco">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Quick Stats</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl hover:shadow-md transition-shadow">
                    <div className="text-2xl font-bold text-green-600 mb-1">{profileData.stats.habitsCompleted}</div>
                    <div className="text-sm text-gray-600">Habits Completed</div>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:shadow-md transition-shadow">
                    <div className="text-2xl font-bold text-blue-600 mb-1">{profileData.stats.challengesWon}</div>
                    <div className="text-sm text-gray-600">Challenges Won</div>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:shadow-md transition-shadow">
                    <div className="text-2xl font-bold text-purple-600 mb-1">{profileData.stats.totalPoints}</div>
                    <div className="text-sm text-gray-600">Points Earned</div>
                  </div>
                  <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl hover:shadow-md transition-shadow">
                    <div className="text-2xl font-bold text-yellow-600 mb-1">{profileData.stats.streakDays}</div>
                    <div className="text-sm text-gray-600">Day Streak</div>
                  </div>
                </div>

                {/* Recent Milestones */}
                <div className="mt-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-3">Recent Milestones 🎯</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-green-500">✅</span>
                      <span>Reached 2000+ eco points</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-blue-500">✅</span>
                      <span>20-day habit streak</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-purple-500">✅</span>
                      <span>Saved 2 tons of CO₂</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'achievements' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Achievements & Badges</h3>
                <div className="text-sm text-gray-600">
                  {achievements.filter(a => a.completed).length} of {achievements.length} completed
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {achievements.map((achievement, index) => (
                  <div
                    key={achievement.title}
                    className={`card border-2 ${getRarityColor(achievement.rarity)} ${
                      achievement.completed ? 'hover-lift' : 'opacity-60'
                    } relative overflow-hidden group`}
                  >
                    {achievement.completed && (
                      <div className="absolute top-2 right-2">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          ✓
                        </div>
                      </div>
                    )}

                    <div className="text-center">
                      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">{achievement.icon}</div>
                      <h3 className="font-semibold text-lg mb-2 text-gray-900">
                        {achievement.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-4">
                        {achievement.description}
                      </p>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>Progress</span>
                          <span>{achievement.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(achievement.progress)}`}
                            style={{ width: `${achievement.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityBadgeColor(achievement.rarity)}`}>
                          {achievement.rarity}
                        </span>
                        <span className="text-sm font-bold text-green-600">
                          {achievement.points} pts
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {achievement.date}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="card-eco">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Recent Activity</h3>
                <button className="text-sm text-green-600 hover:text-green-700 font-medium">
                  View All →
                </button>
              </div>
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-4 p-4 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-gray-200 hover-lift group"
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      activity.type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                    } group-hover:scale-110 transition-transform`}>
                      <span className="text-white text-lg">{activity.icon}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-gray-900">{activity.action}</p>
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          {activity.category}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{activity.details}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{activity.time}</span>
                        <span className="font-medium text-green-600">{activity.points}</span>
                      </div>
                    </div>
                    <div className="text-gray-400 group-hover:text-green-500 transition-colors">
                      →
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Monthly Progress */}
              <div className="card-eco">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">📊</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Monthly Progress</h3>
                </div>
                <div className="space-y-6">
                  <div className="text-center p-6 bg-gradient-to-br from-green-50 to-blue-50 rounded-xl">
                    <div className="text-3xl font-bold text-green-600 mb-2">{profileData.stats.totalPoints}</div>
                    <div className="text-sm text-gray-600 mb-1">Total Eco Points</div>
                    <div className="text-xs text-green-500">+15% from last month</div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Habits Completion</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Weekly Goals</span>
                      <span className="font-medium">92%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Impact Summary */}
              <div className="card-eco">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Impact Summary</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                    <div>
                      <div className="font-medium text-gray-900">Carbon Saved</div>
                      <div className="text-sm text-gray-600">Equivalent to 50 trees</div>
                    </div>
                    <div className="text-2xl">🌳</div>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    <div>
                      <div className="font-medium text-gray-900">Water Conserved</div>
                      <div className="text-sm text-gray-600">Enough for 30 days</div>
                    </div>
                    <div className="text-2xl">💧</div>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                    <div>
                      <div className="font-medium text-gray-900">Energy Saved</div>
                      <div className="text-sm text-gray-600">Powers home for 2 weeks</div>
                    </div>
                    <div className="text-2xl">⚡</div>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                    <div>
                      <div className="font-medium text-gray-900">Waste Reduced</div>
                      <div className="text-sm text-gray-600">15.6 cubic meters saved</div>
                    </div>
                    <div className="text-2xl">♻️</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'social' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Friends & Connections */}
              <div className="card-eco">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900">Friends & Connections</h3>
                  <span className="text-sm text-gray-600">{profileData.stats.friendsCount} friends</span>
                </div>
                <div className="space-y-3">
                  {[
                    { name: 'Sarah Green', avatar: '🌿', status: 'online', points: '2,156' },
                    { name: 'Mike Eco', avatar: '🌱', status: 'offline', points: '1,847' },
                    { name: 'Lisa Nature', avatar: '🌸', status: 'online', points: '2,934' },
                    { name: 'Tom Earth', avatar: '🌍', status: 'online', points: '1,623' }
                  ].map((friend, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="w-10 h-10 bg-gradient-eco rounded-full flex items-center justify-center text-white">
                        {friend.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{friend.name}</div>
                        <div className="text-sm text-gray-600">{friend.points} eco points</div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${friend.status === 'online' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                    </div>
                  ))}
                </div>
                <button className="w-full mt-4 py-2 text-green-600 hover:text-green-700 font-medium">
                  View All Friends →
                </button>
              </div>

              {/* Community Contributions */}
              <div className="card-eco">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Community Contributions</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">📝</span>
                      <div>
                        <div className="font-medium text-gray-900">Eco Tips Shared</div>
                        <div className="text-sm text-gray-600">{profileData.stats.postsCount} posts</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">🏆</span>
                      <div>
                        <div className="font-medium text-gray-900">Challenges Created</div>
                        <div className="text-sm text-gray-600">7 community challenges</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-2xl">👥</span>
                      <div>
                        <div className="font-medium text-gray-900">People Inspired</div>
                        <div className="text-sm text-gray-600">23 eco journeys started</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Avatar Selection Modal */}
        {showAvatarModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-md w-full max-h-96 overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Choose Your Avatar</h3>
                <button
                  onClick={() => setShowAvatarModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <div className="grid grid-cols-6 gap-3">
                {avatarOptions.map((avatar, index) => (
                  <button
                    key={index}
                    onClick={() => handleAvatarSelect(avatar)}
                    className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center text-2xl hover:bg-green-100 hover:scale-110 transition-all"
                  >
                    {avatar}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Share Profile Modal */}
        {showShareModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-md w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Share Your Profile</h3>
                <button
                  onClick={() => setShowShareModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600 mb-2">Share your eco achievements:</p>
                  <p className="text-sm font-medium">
                    "Check out my GreenMate profile! I've saved {ecoStats[0].value} of CO₂ and completed {profileData.stats.habitsCompleted} eco-friendly habits! 🌱"
                  </p>
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={handleShareProfile}
                    className="flex-1 btn-eco py-2"
                  >
                    📋 Copy Link
                  </button>
                  <button className="flex-1 btn-secondary py-2">
                    🐦 Twitter
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
