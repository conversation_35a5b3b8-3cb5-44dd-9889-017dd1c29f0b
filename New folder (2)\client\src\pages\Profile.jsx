import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { AuthContext } from '../contexts/AuthContext';
import {
  FaUser, FaEdit, FaCamera, FaLeaf, FaTrophy, FaFire,
  FaCalendarAlt, FaMapMarkerAlt, FaEnvelope, FaPhone,
  FaGlobe, FaRecycle, FaSeedling, FaWater, FaBolt
} from 'react-icons/fa';

const Profile = () => {
  const { user } = useContext(AuthContext);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [profileData, setProfileData] = useState({
    name: user?.name || 'Eco Warrior',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Passionate about sustainable living and environmental conservation. Making small changes for a big impact! 🌱',
    joinDate: 'January 2024',
    avatar: null
  });

  const ecoStats = [
    { icon: FaLeaf, label: 'CO₂ Saved', value: '2.5 tons', color: 'text-eco-forest', bg: 'bg-eco-forest-100' },
    { icon: FaRecycle, label: 'Items Recycled', value: '1,247', color: 'text-eco-mint', bg: 'bg-eco-mint-100' },
    { icon: FaWater, label: 'Water Saved', value: '850L', color: 'text-eco-sky', bg: 'bg-eco-sky-100' },
    { icon: FaBolt, label: 'Energy Saved', value: '340 kWh', color: 'text-eco-earth', bg: 'bg-eco-earth-100' }
  ];

  const achievements = [
    {
      icon: '🌱',
      title: 'Green Beginner',
      description: 'Started your eco journey',
      date: 'Jan 2024',
      completed: true,
      rarity: 'common'
    },
    {
      icon: '🔥',
      title: '7-Day Streak',
      description: 'Maintained habits for a week',
      date: 'Feb 2024',
      completed: true,
      rarity: 'uncommon'
    },
    {
      icon: '♻️',
      title: 'Recycling Champion',
      description: 'Recycled 1000+ items',
      date: 'Mar 2024',
      completed: true,
      rarity: 'rare'
    },
    {
      icon: '🌍',
      title: 'Planet Protector',
      description: 'Saved 2+ tons of CO₂',
      date: 'In Progress',
      completed: false,
      rarity: 'epic'
    }
  ];

  const recentActivities = [
    {
      icon: FaLeaf,
      action: 'Completed daily eco-challenge',
      time: '2 hours ago',
      points: '+15 pts',
      type: 'success'
    },
    {
      icon: FaRecycle,
      action: 'Recycled plastic bottles',
      time: '5 hours ago',
      points: '+10 pts',
      type: 'info'
    },
    {
      icon: FaSeedling,
      action: 'Planted virtual tree',
      time: '1 day ago',
      points: '+25 pts',
      type: 'success'
    },
    {
      icon: FaWater,
      action: 'Water conservation goal met',
      time: '2 days ago',
      points: '+20 pts',
      type: 'info'
    }
  ];

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    // Here you would typically save to backend
    setIsEditing(false);
    // Show success message
  };

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'common': return 'border-neutral-300 bg-neutral-50';
      case 'uncommon': return 'border-eco-forest-300 bg-eco-forest-50';
      case 'rare': return 'border-eco-sky-300 bg-eco-sky-50';
      case 'epic': return 'border-eco-earth-300 bg-eco-earth-50';
      default: return 'border-neutral-300 bg-neutral-50';
    }
  };

  return (
    <div className="min-h-screen py-xl nature-texture">
      <div className="container mx-auto">
        {/* Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card-eco mb-xl"
        >
          <div className="flex flex-col md:flex-row items-start md:items-center gap-xl">
            {/* Avatar Section */}
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-nature rounded-full flex items-center justify-center text-white text-4xl font-bold shadow-eco-lg animate-breathe">
                {profileData.avatar ? (
                  <img
                    src={profileData.avatar}
                    alt="Profile"
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  profileData.name.charAt(0).toUpperCase()
                )}
              </div>
              <button className="absolute bottom-0 right-0 w-10 h-10 bg-eco-forest-500 rounded-full flex items-center justify-center text-white hover-lift shadow-eco-md">
                <FaCamera className="text-sm" />
              </button>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-lg">
                <div>
                  {isEditing ? (
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="input-field text-2xl font-bold mb-sm"
                    />
                  ) : (
                    <h1 className="text-3xl font-bold font-display text-eco-forest mb-sm animate-slide-up-fade">
                      {profileData.name}
                    </h1>
                  )}
                  <div className="flex items-center gap-md text-neutral-600 mb-md">
                    <span className="flex items-center gap-xs">
                      <FaCalendarAlt className="text-eco-forest" />
                      Joined {profileData.joinDate}
                    </span>
                    <span className="flex items-center gap-xs">
                      <FaMapMarkerAlt className="text-eco-mint" />
                      {profileData.location}
                    </span>
                  </div>
                </div>

                <button
                  onClick={() => isEditing ? handleSave() : setIsEditing(true)}
                  className={`btn ${isEditing ? 'btn-nature' : 'btn-outline'} hover-glow`}
                >
                  <FaEdit />
                  <span>{isEditing ? 'Save Changes' : 'Edit Profile'}</span>
                </button>
              </div>

              {/* Bio */}
              <div className="mb-lg">
                {isEditing ? (
                  <textarea
                    value={profileData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    className="input-field w-full h-24 resize-none"
                    placeholder="Tell us about your eco journey..."
                  />
                ) : (
                  <p className="text-neutral-600 leading-relaxed">
                    {profileData.bio}
                  </p>
                )}
              </div>

              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-md">
                <div className="flex items-center gap-md">
                  <FaEnvelope className="text-eco-forest" />
                  {isEditing ? (
                    <input
                      type="email"
                      value={profileData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="input-field flex-1"
                    />
                  ) : (
                    <span className="text-neutral-600">{profileData.email}</span>
                  )}
                </div>
                <div className="flex items-center gap-md">
                  <FaPhone className="text-eco-mint" />
                  {isEditing ? (
                    <input
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="input-field flex-1"
                    />
                  ) : (
                    <span className="text-neutral-600">{profileData.phone}</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Eco Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-lg mb-xl"
        >
          {ecoStats.map((stat, index) => (
            <div
              key={stat.label}
              className="card-eco text-center hover-lift animate-leaf-grow"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`w-16 h-16 ${stat.bg} rounded-xl flex items-center justify-center mx-auto mb-md`}>
                <stat.icon className={`text-2xl ${stat.color} animate-float-gentle`} />
              </div>
              <div className="text-2xl font-bold text-neutral-900 mb-xs">
                {stat.value}
              </div>
              <div className="text-sm text-neutral-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center mb-xl"
        >
          <div className="flex bg-white rounded-xl p-xs shadow-eco-md">
            {[
              { id: 'overview', label: 'Overview', icon: FaUser },
              { id: 'achievements', label: 'Achievements', icon: FaTrophy },
              { id: 'activity', label: 'Activity', icon: FaFire }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-sm px-lg py-md rounded-lg font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-nature text-white shadow-eco-sm'
                    : 'text-neutral-600 hover:text-eco-forest'
                }`}
              >
                <tab.icon />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-xl">
              {/* Environmental Impact */}
              <div className="card-forest">
                <div className="flex items-center gap-md mb-lg">
                  <div className="w-12 h-12 bg-gradient-forest rounded-xl flex items-center justify-center">
                    <FaGlobe className="text-white text-xl animate-breathe" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900 font-display">
                    Environmental Impact
                  </h3>
                </div>

                <div className="space-y-lg">
                  <div>
                    <div className="flex justify-between text-sm mb-xs">
                      <span className="text-neutral-600">Carbon Footprint Reduction</span>
                      <span className="font-medium text-eco-forest">85%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill progress-fill-forest" style={{ width: '85%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-xs">
                      <span className="text-neutral-600">Sustainability Score</span>
                      <span className="font-medium text-eco-mint">92%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill progress-fill-nature" style={{ width: '92%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-xs">
                      <span className="text-neutral-600">Eco Goals Completed</span>
                      <span className="font-medium text-eco-earth">78%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill progress-fill-earth" style={{ width: '78%' }}></div>
                    </div>
                  </div>
                </div>

                <div className="mt-lg pt-lg border-t border-neutral-200">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-eco-forest mb-xs">Level 12</div>
                    <div className="text-sm text-neutral-600 mb-md">Eco Enthusiast</div>
                    <div className="text-xs text-neutral-500">
                      2,340 / 3,000 XP to next level
                    </div>
                    <div className="progress-bar mt-sm">
                      <div className="progress-fill progress-fill-nature" style={{ width: '78%' }}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="card-ocean">
                <div className="flex items-center gap-md mb-lg">
                  <div className="w-12 h-12 bg-gradient-ocean rounded-xl flex items-center justify-center">
                    <FaLeaf className="text-white text-xl animate-wave-subtle" />
                  </div>
                  <h3 className="text-xl font-semibold text-neutral-900 font-display">
                    This Month's Impact
                  </h3>
                </div>

                <div className="grid grid-cols-2 gap-lg">
                  <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                    <div className="text-2xl font-bold text-eco-forest mb-xs">47</div>
                    <div className="text-sm text-neutral-600">Habits Completed</div>
                  </div>
                  <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                    <div className="text-2xl font-bold text-eco-mint mb-xs">12</div>
                    <div className="text-sm text-neutral-600">Challenges Won</div>
                  </div>
                  <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                    <div className="text-2xl font-bold text-eco-sky mb-xs">890</div>
                    <div className="text-sm text-neutral-600">Points Earned</div>
                  </div>
                  <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                    <div className="text-2xl font-bold text-eco-earth mb-xs">23</div>
                    <div className="text-sm text-neutral-600">Day Streak</div>
                  </div>
                </div>

                <div className="mt-lg">
                  <h4 className="font-semibold mb-md">Recent Milestones</h4>
                  <div className="space-y-sm">
                    <div className="flex items-center gap-md p-sm bg-white bg-opacity-50 rounded-lg">
                      <span className="text-lg">🏆</span>
                      <span className="text-sm text-neutral-600">Reached 1000 eco points</span>
                    </div>
                    <div className="flex items-center gap-md p-sm bg-white bg-opacity-50 rounded-lg">
                      <span className="text-lg">🌱</span>
                      <span className="text-sm text-neutral-600">Planted 50th virtual tree</span>
                    </div>
                    <div className="flex items-center gap-md p-sm bg-white bg-opacity-50 rounded-lg">
                      <span className="text-lg">♻️</span>
                      <span className="text-sm text-neutral-600">Recycling champion badge</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'achievements' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className={`card border-2 ${getRarityColor(achievement.rarity)} ${
                    achievement.completed ? 'hover-lift' : 'opacity-60'
                  } relative overflow-hidden`}
                >
                  {achievement.completed && (
                    <div className="absolute top-0 right-0 w-0 h-0 border-l-[30px] border-l-transparent border-t-[30px] border-t-success">
                      <div className="absolute -top-6 -right-1 text-white text-xs">✓</div>
                    </div>
                  )}

                  <div className="text-center">
                    <div className="text-4xl mb-md animate-float-gentle">
                      {achievement.icon}
                    </div>
                    <h3 className="font-semibold text-lg mb-sm text-neutral-900">
                      {achievement.title}
                    </h3>
                    <p className="text-sm text-neutral-600 mb-md">
                      {achievement.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className={`badge badge-sm ${
                        achievement.rarity === 'common' ? 'badge-secondary' :
                        achievement.rarity === 'uncommon' ? 'badge-success' :
                        achievement.rarity === 'rare' ? 'badge-info' :
                        'badge-warning'
                      }`}>
                        {achievement.rarity}
                      </span>
                      <span className="text-xs text-neutral-500">
                        {achievement.date}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {activeTab === 'activity' && (
            <div className="card-eco">
              <div className="flex items-center gap-md mb-xl">
                <div className="w-12 h-12 bg-gradient-nature rounded-xl flex items-center justify-center">
                  <FaFire className="text-white text-xl animate-pulse-eco" />
                </div>
                <h3 className="text-2xl font-semibold text-neutral-900 font-display">
                  Recent Activity
                </h3>
              </div>

              <div className="space-y-lg">
                {recentActivities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-lg p-lg bg-gradient-to-r from-white to-neutral-50 rounded-xl border border-neutral-200 hover-lift"
                  >
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      activity.type === 'success' ? 'bg-success' : 'bg-info'
                    }`}>
                      <activity.icon className="text-white animate-wave-subtle" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-neutral-900 mb-xs">
                        {activity.action}
                      </p>
                      <p className="text-sm text-neutral-500">
                        {activity.time}
                      </p>
                    </div>
                    <div className={`font-bold ${
                      activity.type === 'success' ? 'text-success' : 'text-info'
                    }`}>
                      {activity.points}
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-xl text-center">
                <button className="btn-outline hover-glow">
                  <span>View All Activity</span>
                  <span className="animate-wave-subtle">📈</span>
                </button>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Profile;
