import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { AuthContext } from '../contexts/AuthContext';
import {
  <PERSON>a<PERSON>og, <PERSON>a<PERSON>ser, FaBell, FaShieldAlt, FaPalette, FaGlobe,
  FaLeaf, FaToggleOn, FaToggleOff, FaSave, FaTrash,
  FaMoon, FaSun, FaLanguage, FaLock, FaEye, FaEyeSlash,
  FaDownload, FaUpload, FaSync, FaQuestionCircle
} from 'react-icons/fa';

const Settings = () => {
  const { user } = useContext(AuthContext);
  const [activeSection, setActiveSection] = useState('profile');
  const [settings, setSettings] = useState({
    profile: {
      displayName: user?.name || 'Eco Warrior',
      email: user?.email || '<EMAIL>',
      bio: 'Passionate about sustainable living',
      publicProfile: true,
      showEmail: false,
      showProgress: true
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReport: true,
      achievementAlerts: true,
      reminderNotifications: true,
      communityUpdates: false,
      marketingEmails: false
    },
    privacy: {
      profileVisibility: 'public',
      activityTracking: true,
      dataCollection: true,
      shareProgress: true,
      allowFriendRequests: true,
      showOnLeaderboard: true
    },
    appearance: {
      theme: 'eco',
      darkMode: false,
      language: 'en',
      animations: true,
      compactMode: false,
      colorBlindMode: false
    },
    eco: {
      carbonTracking: true,
      waterTracking: true,
      wasteTracking: true,
      energyTracking: true,
      autoGoals: true,
      smartReminders: true,
      impactSharing: true
    },
    account: {
      twoFactorAuth: false,
      loginAlerts: true,
      sessionTimeout: '30',
      dataExport: false,
      accountDeletion: false
    }
  });

  const [showPassword, setShowPassword] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const settingSections = [
    { id: 'profile', label: 'Profile', icon: FaUser, color: 'text-eco-forest' },
    { id: 'notifications', label: 'Notifications', icon: FaBell, color: 'text-eco-mint' },
    { id: 'privacy', label: 'Privacy & Security', icon: FaShieldAlt, color: 'text-eco-sky' },
    { id: 'appearance', label: 'Appearance', icon: FaPalette, color: 'text-eco-earth' },
    { id: 'eco', label: 'Eco Preferences', icon: FaLeaf, color: 'text-eco-forest' },
    { id: 'account', label: 'Account', icon: FaLock, color: 'text-neutral-600' }
  ];

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = () => {
    // Here you would typically save to backend
    console.log('Saving settings:', settings);
    setHasChanges(false);
    // Show success message
  };

  const handleResetSection = (section) => {
    // Reset to default values
    console.log('Resetting section:', section);
    setHasChanges(true);
  };

  const ToggleSwitch = ({ enabled, onChange, label, description }) => (
    <div className="flex items-center justify-between p-md bg-white bg-opacity-50 rounded-lg hover-lift">
      <div className="flex-1">
        <div className="font-medium text-neutral-900 mb-xs">{label}</div>
        {description && (
          <div className="text-sm text-neutral-600">{description}</div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          enabled ? 'bg-eco-forest-500' : 'bg-neutral-300'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  const SelectField = ({ value, onChange, options, label, description }) => (
    <div className="p-md bg-white bg-opacity-50 rounded-lg">
      <div className="font-medium text-neutral-900 mb-xs">{label}</div>
      {description && (
        <div className="text-sm text-neutral-600 mb-sm">{description}</div>
      )}
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="input-field w-full"
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );

  return (
    <div className="min-h-screen py-xl nature-texture">
      <div className="container mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card-eco mb-xl"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-md">
              <div className="w-12 h-12 bg-gradient-nature rounded-xl flex items-center justify-center">
                <FaCog className="text-white text-xl animate-spin" style={{ animationDuration: '3s' }} />
              </div>
              <div>
                <h1 className="text-3xl font-bold font-display text-eco-forest">Settings</h1>
                <p className="text-neutral-600">Customize your GreenMate experience</p>
              </div>
            </div>

            {hasChanges && (
              <motion.button
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                onClick={handleSaveSettings}
                className="btn btn-nature hover-glow"
              >
                <FaSave />
                <span>Save Changes</span>
              </motion.button>
            )}
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-xl">
          {/* Settings Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <div className="card-forest sticky top-4">
              <h3 className="font-semibold text-lg mb-lg text-neutral-900">Settings Menu</h3>
              <nav className="space-y-sm">
                {settingSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-md p-md rounded-lg text-left transition-all ${
                      activeSection === section.id
                        ? 'bg-gradient-nature text-white shadow-eco-sm'
                        : 'text-neutral-600 hover:bg-white hover:bg-opacity-50'
                    }`}
                  >
                    <section.icon className={activeSection === section.id ? 'text-white' : section.color} />
                    <span className="font-medium">{section.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </motion.div>

          {/* Settings Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:col-span-3"
          >
            {activeSection === 'profile' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Profile Settings</h2>
                  <button
                    onClick={() => handleResetSection('profile')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <div className="p-md bg-white bg-opacity-50 rounded-lg">
                    <label className="block font-medium text-neutral-900 mb-sm">Display Name</label>
                    <input
                      type="text"
                      value={settings.profile.displayName}
                      onChange={(e) => handleSettingChange('profile', 'displayName', e.target.value)}
                      className="input-field w-full"
                    />
                  </div>

                  <div className="p-md bg-white bg-opacity-50 rounded-lg">
                    <label className="block font-medium text-neutral-900 mb-sm">Email</label>
                    <input
                      type="email"
                      value={settings.profile.email}
                      onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                      className="input-field w-full"
                    />
                  </div>

                  <div className="p-md bg-white bg-opacity-50 rounded-lg">
                    <label className="block font-medium text-neutral-900 mb-sm">Bio</label>
                    <textarea
                      value={settings.profile.bio}
                      onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
                      className="input-field w-full h-24 resize-none"
                      placeholder="Tell others about your eco journey..."
                    />
                  </div>

                  <ToggleSwitch
                    enabled={settings.profile.publicProfile}
                    onChange={(value) => handleSettingChange('profile', 'publicProfile', value)}
                    label="Public Profile"
                    description="Allow others to view your profile and eco achievements"
                  />

                  <ToggleSwitch
                    enabled={settings.profile.showEmail}
                    onChange={(value) => handleSettingChange('profile', 'showEmail', value)}
                    label="Show Email"
                    description="Display your email address on your public profile"
                  />

                  <ToggleSwitch
                    enabled={settings.profile.showProgress}
                    onChange={(value) => handleSettingChange('profile', 'showProgress', value)}
                    label="Show Progress"
                    description="Display your eco progress and statistics publicly"
                  />
                </div>
              </div>
            )}

            {activeSection === 'notifications' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Notification Settings</h2>
                  <button
                    onClick={() => handleResetSection('notifications')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <ToggleSwitch
                    enabled={settings.notifications.emailNotifications}
                    onChange={(value) => handleSettingChange('notifications', 'emailNotifications', value)}
                    label="Email Notifications"
                    description="Receive important updates via email"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.pushNotifications}
                    onChange={(value) => handleSettingChange('notifications', 'pushNotifications', value)}
                    label="Push Notifications"
                    description="Get instant notifications on your device"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.weeklyReport}
                    onChange={(value) => handleSettingChange('notifications', 'weeklyReport', value)}
                    label="Weekly Progress Report"
                    description="Receive a summary of your eco achievements each week"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.achievementAlerts}
                    onChange={(value) => handleSettingChange('notifications', 'achievementAlerts', value)}
                    label="Achievement Alerts"
                    description="Get notified when you unlock new achievements"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.reminderNotifications}
                    onChange={(value) => handleSettingChange('notifications', 'reminderNotifications', value)}
                    label="Habit Reminders"
                    description="Receive reminders to complete your daily eco habits"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.communityUpdates}
                    onChange={(value) => handleSettingChange('notifications', 'communityUpdates', value)}
                    label="Community Updates"
                    description="Stay updated with community challenges and events"
                  />

                  <ToggleSwitch
                    enabled={settings.notifications.marketingEmails}
                    onChange={(value) => handleSettingChange('notifications', 'marketingEmails', value)}
                    label="Marketing Emails"
                    description="Receive promotional content and eco tips"
                  />
                </div>
              </div>
            )}

            {activeSection === 'privacy' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Privacy & Security</h2>
                  <button
                    onClick={() => handleResetSection('privacy')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <SelectField
                    value={settings.privacy.profileVisibility}
                    onChange={(value) => handleSettingChange('privacy', 'profileVisibility', value)}
                    label="Profile Visibility"
                    description="Control who can see your profile"
                    options={[
                      { value: 'public', label: 'Public - Anyone can view' },
                      { value: 'friends', label: 'Friends Only' },
                      { value: 'private', label: 'Private - Only you' }
                    ]}
                  />

                  <ToggleSwitch
                    enabled={settings.privacy.activityTracking}
                    onChange={(value) => handleSettingChange('privacy', 'activityTracking', value)}
                    label="Activity Tracking"
                    description="Allow GreenMate to track your eco activities for better insights"
                  />

                  <ToggleSwitch
                    enabled={settings.privacy.dataCollection}
                    onChange={(value) => handleSettingChange('privacy', 'dataCollection', value)}
                    label="Anonymous Data Collection"
                    description="Help improve GreenMate by sharing anonymous usage data"
                  />

                  <ToggleSwitch
                    enabled={settings.privacy.shareProgress}
                    onChange={(value) => handleSettingChange('privacy', 'shareProgress', value)}
                    label="Share Progress"
                    description="Allow your progress to be shared in community features"
                  />

                  <ToggleSwitch
                    enabled={settings.privacy.allowFriendRequests}
                    onChange={(value) => handleSettingChange('privacy', 'allowFriendRequests', value)}
                    label="Allow Friend Requests"
                    description="Let other users send you friend requests"
                  />

                  <ToggleSwitch
                    enabled={settings.privacy.showOnLeaderboard}
                    onChange={(value) => handleSettingChange('privacy', 'showOnLeaderboard', value)}
                    label="Show on Leaderboard"
                    description="Display your achievements on public leaderboards"
                  />
                </div>
              </div>
            )}

            {activeSection === 'appearance' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Appearance Settings</h2>
                  <button
                    onClick={() => handleResetSection('appearance')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <SelectField
                    value={settings.appearance.theme}
                    onChange={(value) => handleSettingChange('appearance', 'theme', value)}
                    label="Theme"
                    description="Choose your preferred color theme"
                    options={[
                      { value: 'eco', label: '🌿 Eco Green (Default)' },
                      { value: 'ocean', label: '🌊 Ocean Blue' },
                      { value: 'forest', label: '🌲 Forest Dark' },
                      { value: 'earth', label: '🌍 Earth Tones' }
                    ]}
                  />

                  <ToggleSwitch
                    enabled={settings.appearance.darkMode}
                    onChange={(value) => handleSettingChange('appearance', 'darkMode', value)}
                    label="Dark Mode"
                    description="Use dark theme for better viewing in low light"
                  />

                  <SelectField
                    value={settings.appearance.language}
                    onChange={(value) => handleSettingChange('appearance', 'language', value)}
                    label="Language"
                    description="Choose your preferred language"
                    options={[
                      { value: 'en', label: '🇺🇸 English' },
                      { value: 'es', label: '🇪🇸 Español' },
                      { value: 'fr', label: '🇫🇷 Français' },
                      { value: 'de', label: '🇩🇪 Deutsch' },
                      { value: 'zh', label: '🇨🇳 中文' }
                    ]}
                  />

                  <ToggleSwitch
                    enabled={settings.appearance.animations}
                    onChange={(value) => handleSettingChange('appearance', 'animations', value)}
                    label="Animations"
                    description="Enable smooth animations and transitions"
                  />

                  <ToggleSwitch
                    enabled={settings.appearance.compactMode}
                    onChange={(value) => handleSettingChange('appearance', 'compactMode', value)}
                    label="Compact Mode"
                    description="Use a more compact layout to fit more content"
                  />

                  <ToggleSwitch
                    enabled={settings.appearance.colorBlindMode}
                    onChange={(value) => handleSettingChange('appearance', 'colorBlindMode', value)}
                    label="Color Blind Friendly"
                    description="Use patterns and symbols in addition to colors"
                  />
                </div>
              </div>
            )}

            {activeSection === 'eco' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Eco Preferences</h2>
                  <button
                    onClick={() => handleResetSection('eco')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <ToggleSwitch
                    enabled={settings.eco.carbonTracking}
                    onChange={(value) => handleSettingChange('eco', 'carbonTracking', value)}
                    label="Carbon Footprint Tracking"
                    description="Track and monitor your carbon emissions"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.waterTracking}
                    onChange={(value) => handleSettingChange('eco', 'waterTracking', value)}
                    label="Water Usage Tracking"
                    description="Monitor your water consumption and conservation"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.wasteTracking}
                    onChange={(value) => handleSettingChange('eco', 'wasteTracking', value)}
                    label="Waste Tracking"
                    description="Track recycling and waste reduction efforts"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.energyTracking}
                    onChange={(value) => handleSettingChange('eco', 'energyTracking', value)}
                    label="Energy Usage Tracking"
                    description="Monitor energy consumption and renewable usage"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.autoGoals}
                    onChange={(value) => handleSettingChange('eco', 'autoGoals', value)}
                    label="Automatic Goal Setting"
                    description="Let GreenMate suggest personalized eco goals"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.smartReminders}
                    onChange={(value) => handleSettingChange('eco', 'smartReminders', value)}
                    label="Smart Reminders"
                    description="Receive intelligent reminders based on your habits"
                  />

                  <ToggleSwitch
                    enabled={settings.eco.impactSharing}
                    onChange={(value) => handleSettingChange('eco', 'impactSharing', value)}
                    label="Impact Sharing"
                    description="Share your environmental impact with the community"
                  />
                </div>
              </div>
            )}

            {activeSection === 'account' && (
              <div className="card-eco">
                <div className="flex items-center justify-between mb-xl">
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">Account Settings</h2>
                  <button
                    onClick={() => handleResetSection('account')}
                    className="btn btn-secondary hover-lift"
                  >
                    <FaSync />
                    <span>Reset</span>
                  </button>
                </div>

                <div className="space-y-lg">
                  <div className="p-md bg-white bg-opacity-50 rounded-lg">
                    <div className="flex items-center justify-between mb-md">
                      <div>
                        <div className="font-medium text-neutral-900">Change Password</div>
                        <div className="text-sm text-neutral-600">Update your account password</div>
                      </div>
                      <button className="btn btn-outline hover-lift">
                        <FaLock />
                        <span>Change</span>
                      </button>
                    </div>
                  </div>

                  <ToggleSwitch
                    enabled={settings.account.twoFactorAuth}
                    onChange={(value) => handleSettingChange('account', 'twoFactorAuth', value)}
                    label="Two-Factor Authentication"
                    description="Add an extra layer of security to your account"
                  />

                  <ToggleSwitch
                    enabled={settings.account.loginAlerts}
                    onChange={(value) => handleSettingChange('account', 'loginAlerts', value)}
                    label="Login Alerts"
                    description="Get notified when someone logs into your account"
                  />

                  <SelectField
                    value={settings.account.sessionTimeout}
                    onChange={(value) => handleSettingChange('account', 'sessionTimeout', value)}
                    label="Session Timeout"
                    description="Automatically log out after inactivity"
                    options={[
                      { value: '15', label: '15 minutes' },
                      { value: '30', label: '30 minutes' },
                      { value: '60', label: '1 hour' },
                      { value: '240', label: '4 hours' },
                      { value: 'never', label: 'Never' }
                    ]}
                  />

                  <div className="p-md bg-white bg-opacity-50 rounded-lg">
                    <div className="flex items-center justify-between mb-md">
                      <div>
                        <div className="font-medium text-neutral-900">Export Data</div>
                        <div className="text-sm text-neutral-600">Download all your GreenMate data</div>
                      </div>
                      <button className="btn btn-outline hover-lift">
                        <FaDownload />
                        <span>Export</span>
                      </button>
                    </div>
                  </div>

                  <div className="p-md bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center justify-between mb-md">
                      <div>
                        <div className="font-medium text-red-900">Delete Account</div>
                        <div className="text-sm text-red-600">Permanently delete your account and all data</div>
                      </div>
                      <button className="btn bg-red-500 text-white hover:bg-red-600">
                        <FaTrash />
                        <span>Delete</span>
                      </button>
                    </div>
                    <div className="text-xs text-red-500">
                      ⚠️ This action cannot be undone. All your progress will be lost.
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="card-ocean mt-xl"
        >
          <div className="flex items-center gap-md mb-lg">
            <div className="w-12 h-12 bg-gradient-ocean rounded-xl flex items-center justify-center">
              <FaQuestionCircle className="text-white text-xl animate-float-gentle" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-neutral-900 font-display">Need Help?</h3>
              <p className="text-neutral-600">Get support or learn more about GreenMate features</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-lg">
            <button className="p-lg bg-white bg-opacity-50 rounded-xl text-center hover-lift">
              <div className="text-2xl mb-sm">📚</div>
              <div className="font-medium text-neutral-900 mb-xs">User Guide</div>
              <div className="text-sm text-neutral-600">Learn how to use all features</div>
            </button>

            <button className="p-lg bg-white bg-opacity-50 rounded-xl text-center hover-lift">
              <div className="text-2xl mb-sm">💬</div>
              <div className="font-medium text-neutral-900 mb-xs">Contact Support</div>
              <div className="text-sm text-neutral-600">Get help from our team</div>
            </button>

            <button className="p-lg bg-white bg-opacity-50 rounded-xl text-center hover-lift">
              <div className="text-2xl mb-sm">🌱</div>
              <div className="font-medium text-neutral-900 mb-xs">Eco Tips</div>
              <div className="text-sm text-neutral-600">Discover new ways to be eco-friendly</div>
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Settings;