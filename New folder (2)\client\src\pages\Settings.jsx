import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import toast from 'react-hot-toast';

const Settings = () => {
  const { user, logout } = useAuth();
  const { currentLanguage, changeLanguage, availableLanguages } = useLanguage();
  const [activeSection, setActiveSection] = useState('profile');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [settings, setSettings] = useState({
    // Profile Settings
    profile: {
      name: user?.name || 'Eco Warrior',
      email: user?.email || '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      bio: 'Passionate about sustainable living and environmental conservation.',
      website: 'https://eco-warrior.com',
      timezone: 'America/Los_Angeles',
      language: currentLanguage || 'en',
      dateFormat: 'MM/DD/YYYY',
      currency: 'USD'
    },

    // Privacy Settings
    privacy: {
      profileVisibility: 'public',
      showEmail: false,
      showPhone: false,
      showLocation: true,
      showStats: true,
      allowFriendRequests: true,
      showOnlineStatus: true,
      dataCollection: true,
      analyticsTracking: true,
      marketingEmails: false,
      searchEngineIndexing: true
    },

    // Notification Settings
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      habitReminders: true,
      challengeUpdates: true,
      friendActivity: true,
      weeklyReports: true,
      monthlyReports: true,
      achievementAlerts: true,
      communityUpdates: false,
      marketingUpdates: false,
      systemUpdates: true,
      reminderTime: '09:00',
      reminderDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      quietHours: {
        enabled: true,
        start: '22:00',
        end: '07:00'
      }
    },

    // App Preferences
    preferences: {
      theme: 'light',
      colorScheme: 'green',
      fontSize: 'medium',
      animations: true,
      soundEffects: true,
      hapticFeedback: true,
      autoSave: true,
      offlineMode: false,
      dataSync: true,
      backgroundSync: true,
      lowDataMode: false,
      accessibilityMode: false,
      highContrast: false,
      screenReader: false,
      keyboardNavigation: false
    },

    // Security Settings
    security: {
      twoFactorAuth: false,
      loginAlerts: true,
      sessionTimeout: 30,
      passwordExpiry: 90,
      deviceTracking: true,
      suspiciousActivityAlerts: true,
      dataEncryption: true,
      secureConnection: true,
      biometricAuth: false,
      trustedDevices: []
    },

    // Data & Storage
    data: {
      autoBackup: true,
      backupFrequency: 'weekly',
      cloudSync: true,
      localStorage: true,
      cacheSize: '100MB',
      dataRetention: '2years',
      exportFormat: 'json',
      compressionEnabled: true
    }
  });

  const settingSections = [
    {
      id: 'profile',
      title: 'Profile',
      icon: '👤',
      description: 'Personal information and account details'
    },
    {
      id: 'privacy',
      title: 'Privacy',
      icon: '🔒',
      description: 'Control who can see your information'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: '🔔',
      description: 'Manage alerts and reminders'
    },
    {
      id: 'preferences',
      title: 'Preferences',
      icon: '🎨',
      description: 'Customize your app experience'
    },
    {
      id: 'security',
      title: 'Security',
      icon: '🛡️',
      description: 'Account security and authentication'
    },
    {
      id: 'data',
      title: 'Data & Storage',
      icon: '💾',
      description: 'Backup, sync, and data management'
    },
    {
      id: 'advanced',
      title: 'Advanced',
      icon: '⚙️',
      description: 'Developer options and advanced features'
    }
  ];



  const themes = [
    { id: 'light', name: 'Light', preview: 'bg-white border-gray-200' },
    { id: 'dark', name: 'Dark', preview: 'bg-gray-900 border-gray-700' },
    { id: 'auto', name: 'Auto', preview: 'bg-gradient-to-r from-white to-gray-900' }
  ];

  const colorSchemes = [
    { id: 'green', name: 'Green', color: 'bg-green-500' },
    { id: 'blue', name: 'Blue', color: 'bg-blue-500' },
    { id: 'purple', name: 'Purple', color: 'bg-purple-500' },
    { id: 'orange', name: 'Orange', color: 'bg-orange-500' },
    { id: 'pink', name: 'Pink', color: 'bg-pink-500' },
    { id: 'teal', name: 'Teal', color: 'bg-teal-500' }
  ];

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));

    // Special handling for language changes
    if (section === 'profile' && key === 'language') {
      changeLanguage(value);
    }

    toast.success('Setting updated! ✅');
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Settings saved successfully! 🎉');
    } catch (error) {
      toast.error('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
      // Reset to default settings
      toast.success('Settings reset to default! 🔄');
    }
  };

  const handleExportData = () => {
    const dataToExport = {
      profile: settings.profile,
      preferences: settings.preferences,
      exportDate: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `greenmate-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Data exported successfully! 📁');
    setShowExportModal(false);
  };

  const handleDeleteAccount = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Account deletion initiated. You will receive a confirmation email.');
      setShowDeleteModal(false);
      logout();
    } catch (error) {
      toast.error('Failed to delete account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-green-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings ⚙️</h1>
          <p className="text-gray-600">Customize your GreenMate experience and manage your account</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <div className="card-eco sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Settings</h3>
              <nav className="space-y-2">
                {settingSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left p-3 rounded-lg transition-all ${
                      activeSection === section.id
                        ? 'bg-gradient-eco text-white shadow-md'
                        : 'text-gray-700 hover:bg-green-50 hover:text-green-700'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{section.icon}</span>
                      <div>
                        <div className="font-medium">{section.title}</div>
                        <div className={`text-xs ${
                          activeSection === section.id ? 'text-green-100' : 'text-gray-500'
                        }`}>
                          {section.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </nav>

              {/* Quick Actions */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h4>
                <div className="space-y-2">
                  <button
                    onClick={handleSaveSettings}
                    disabled={isLoading}
                    className="w-full btn-eco py-2 text-sm disabled:opacity-50"
                  >
                    {isLoading ? '💾 Saving...' : '💾 Save All'}
                  </button>
                  <button
                    onClick={() => setShowExportModal(true)}
                    className="w-full btn-secondary py-2 text-sm"
                  >
                    📁 Export Data
                  </button>
                  <button
                    onClick={handleResetSettings}
                    className="w-full btn-outline py-2 text-sm text-gray-600 hover:text-red-600"
                  >
                    🔄 Reset All
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            {/* Profile Settings */}
            {activeSection === 'profile' && (
              <div className="space-y-6">
                <div className="card-eco">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                      <span className="text-white text-xl">👤</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">Profile Information</h3>
                      <p className="text-gray-600">Update your personal details and preferences</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                      <input
                        type="text"
                        value={settings.profile.name}
                        onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
                        className="input-eco"
                        placeholder="Enter your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                      <input
                        type="email"
                        value={settings.profile.email}
                        onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                        className="input-eco"
                        placeholder="Enter your email"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                      <input
                        type="tel"
                        value={settings.profile.phone}
                        onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
                        className="input-eco"
                        placeholder="Enter your phone number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                      <input
                        type="text"
                        value={settings.profile.location}
                        onChange={(e) => handleSettingChange('profile', 'location', e.target.value)}
                        className="input-eco"
                        placeholder="Enter your location"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                      <input
                        type="url"
                        value={settings.profile.website}
                        onChange={(e) => handleSettingChange('profile', 'website', e.target.value)}
                        className="input-eco"
                        placeholder="Enter your website URL"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                      <select
                        value={settings.profile.language}
                        onChange={(e) => handleSettingChange('profile', 'language', e.target.value)}
                        className="input-eco"
                      >
                        {availableLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.flag} {lang.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                    <textarea
                      value={settings.profile.bio}
                      onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
                      className="input-eco"
                      rows="3"
                      placeholder="Tell us about yourself and your eco journey..."
                    />
                  </div>
                </div>

                {/* Regional Settings */}
                <div className="card-eco">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Regional Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                      <select
                        value={settings.profile.timezone}
                        onChange={(e) => handleSettingChange('profile', 'timezone', e.target.value)}
                        className="input-eco"
                      >
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="Europe/London">Greenwich Mean Time (GMT)</option>
                        <option value="Europe/Paris">Central European Time (CET)</option>
                        <option value="Asia/Tokyo">Japan Standard Time (JST)</option>
                        <option value="Asia/Shanghai">China Standard Time (CST)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                      <select
                        value={settings.profile.dateFormat}
                        onChange={(e) => handleSettingChange('profile', 'dateFormat', e.target.value)}
                        className="input-eco"
                      >
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        <option value="DD MMM YYYY">DD MMM YYYY</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                      <select
                        value={settings.profile.currency}
                        onChange={(e) => handleSettingChange('profile', 'currency', e.target.value)}
                        className="input-eco"
                      >
                        <option value="USD">🇺🇸 USD - US Dollar</option>
                        <option value="EUR">🇪🇺 EUR - Euro</option>
                        <option value="GBP">🇬🇧 GBP - British Pound</option>
                        <option value="JPY">🇯🇵 JPY - Japanese Yen</option>
                        <option value="CAD">🇨🇦 CAD - Canadian Dollar</option>
                        <option value="AUD">🇦🇺 AUD - Australian Dollar</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Privacy Settings */}
            {activeSection === 'privacy' && (
              <div className="space-y-6">
                <div className="card-eco">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                      <span className="text-white text-xl">🔒</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">Privacy Settings</h3>
                      <p className="text-gray-600">Control who can see your information and activity</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Profile Visibility */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Profile Visibility</label>
                      <div className="space-y-3">
                        {[
                          { value: 'public', label: 'Public', desc: 'Anyone can see your profile' },
                          { value: 'friends', label: 'Friends Only', desc: 'Only your friends can see your profile' },
                          { value: 'private', label: 'Private', desc: 'Only you can see your profile' }
                        ].map((option) => (
                          <label key={option.value} className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input
                              type="radio"
                              name="profileVisibility"
                              value={option.value}
                              checked={settings.privacy.profileVisibility === option.value}
                              onChange={(e) => handleSettingChange('privacy', 'profileVisibility', e.target.value)}
                              className="mt-1"
                            />
                            <div>
                              <div className="font-medium text-gray-900">{option.label}</div>
                              <div className="text-sm text-gray-600">{option.desc}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Information Visibility */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Information Visibility</label>
                      <div className="space-y-3">
                        {[
                          { key: 'showEmail', label: 'Show Email Address', desc: 'Display your email on your profile' },
                          { key: 'showPhone', label: 'Show Phone Number', desc: 'Display your phone number on your profile' },
                          { key: 'showLocation', label: 'Show Location', desc: 'Display your location on your profile' },
                          { key: 'showStats', label: 'Show Statistics', desc: 'Display your eco stats and achievements' },
                          { key: 'showOnlineStatus', label: 'Show Online Status', desc: 'Let others see when you\'re online' }
                        ].map((setting) => (
                          <div key={setting.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{setting.label}</div>
                              <div className="text-sm text-gray-600">{setting.desc}</div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={settings.privacy[setting.key]}
                                onChange={(e) => handleSettingChange('privacy', setting.key, e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Data & Analytics */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Data & Analytics</label>
                      <div className="space-y-3">
                        {[
                          { key: 'dataCollection', label: 'Data Collection', desc: 'Allow us to collect usage data to improve the app' },
                          { key: 'analyticsTracking', label: 'Analytics Tracking', desc: 'Help us understand how you use the app' },
                          { key: 'marketingEmails', label: 'Marketing Emails', desc: 'Receive promotional emails and offers' },
                          { key: 'searchEngineIndexing', label: 'Search Engine Indexing', desc: 'Allow search engines to index your public profile' }
                        ].map((setting) => (
                          <div key={setting.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{setting.label}</div>
                              <div className="text-sm text-gray-600">{setting.desc}</div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={settings.privacy[setting.key]}
                                onChange={(e) => handleSettingChange('privacy', setting.key, e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeSection === 'notifications' && (
              <div className="space-y-6">
                <div className="card-eco">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                      <span className="text-white text-xl">🔔</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">Notification Preferences</h3>
                      <p className="text-gray-600">Choose how and when you want to be notified</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Notification Types */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Notification Methods</label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {[
                          { key: 'emailNotifications', label: 'Email', icon: '📧', desc: 'Receive notifications via email' },
                          { key: 'pushNotifications', label: 'Push', icon: '📱', desc: 'Receive push notifications on your device' },
                          { key: 'smsNotifications', label: 'SMS', icon: '💬', desc: 'Receive notifications via text message' }
                        ].map((method) => (
                          <div key={method.key} className="p-4 border border-gray-200 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-lg">{method.icon}</span>
                                <span className="font-medium text-gray-900">{method.label}</span>
                              </div>
                              <label className="relative inline-flex items-center cursor-pointer">
                                <input
                                  type="checkbox"
                                  checked={settings.notifications[method.key]}
                                  onChange={(e) => handleSettingChange('notifications', method.key, e.target.checked)}
                                  className="sr-only peer"
                                />
                                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                              </label>
                            </div>
                            <p className="text-sm text-gray-600">{method.desc}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Notification Categories */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Notification Categories</label>
                      <div className="space-y-3">
                        {[
                          { key: 'habitReminders', label: 'Habit Reminders', desc: 'Daily reminders for your eco habits' },
                          { key: 'challengeUpdates', label: 'Challenge Updates', desc: 'Updates on challenges you\'re participating in' },
                          { key: 'friendActivity', label: 'Friend Activity', desc: 'When friends complete habits or achievements' },
                          { key: 'achievementAlerts', label: 'Achievement Alerts', desc: 'When you unlock new badges or achievements' },
                          { key: 'weeklyReports', label: 'Weekly Reports', desc: 'Weekly summary of your eco progress' },
                          { key: 'monthlyReports', label: 'Monthly Reports', desc: 'Monthly environmental impact reports' },
                          { key: 'communityUpdates', label: 'Community Updates', desc: 'Updates from the GreenMate community' },
                          { key: 'systemUpdates', label: 'System Updates', desc: 'Important app updates and announcements' }
                        ].map((category) => (
                          <div key={category.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{category.label}</div>
                              <div className="text-sm text-gray-600">{category.desc}</div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={settings.notifications[category.key]}
                                onChange={(e) => handleSettingChange('notifications', category.key, e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Reminder Schedule */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Reminder Schedule</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Reminder Time</label>
                          <input
                            type="time"
                            value={settings.notifications.reminderTime}
                            onChange={(e) => handleSettingChange('notifications', 'reminderTime', e.target.value)}
                            className="input-eco"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Reminder Days</label>
                          <div className="flex flex-wrap gap-2">
                            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                              <label key={day} className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={settings.notifications.reminderDays.includes(day)}
                                  onChange={(e) => {
                                    const days = settings.notifications.reminderDays;
                                    if (e.target.checked) {
                                      handleSettingChange('notifications', 'reminderDays', [...days, day]);
                                    } else {
                                      handleSettingChange('notifications', 'reminderDays', days.filter(d => d !== day));
                                    }
                                  }}
                                  className="mr-2"
                                />
                                <span className="text-sm capitalize">{day.slice(0, 3)}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Quiet Hours */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Quiet Hours</label>
                      <div className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <div className="font-medium text-gray-900">Enable Quiet Hours</div>
                            <div className="text-sm text-gray-600">No notifications during specified hours</div>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={settings.notifications.quietHours.enabled}
                              onChange={(e) => handleSettingChange('notifications', 'quietHours', {
                                ...settings.notifications.quietHours,
                                enabled: e.target.checked
                              })}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                          </label>
                        </div>
                        {settings.notifications.quietHours.enabled && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                              <input
                                type="time"
                                value={settings.notifications.quietHours.start}
                                onChange={(e) => handleSettingChange('notifications', 'quietHours', {
                                  ...settings.notifications.quietHours,
                                  start: e.target.value
                                })}
                                className="input-eco"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                              <input
                                type="time"
                                value={settings.notifications.quietHours.end}
                                onChange={(e) => handleSettingChange('notifications', 'quietHours', {
                                  ...settings.notifications.quietHours,
                                  end: e.target.value
                                })}
                                className="input-eco"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Preferences Settings */}
            {activeSection === 'preferences' && (
              <div className="space-y-6">
                <div className="card-eco">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                      <span className="text-white text-xl">🎨</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">App Preferences</h3>
                      <p className="text-gray-600">Customize your app experience and interface</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Theme Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Theme</label>
                      <div className="grid grid-cols-3 gap-4">
                        {themes.map((theme) => (
                          <label key={theme.id} className="cursor-pointer">
                            <input
                              type="radio"
                              name="theme"
                              value={theme.id}
                              checked={settings.preferences.theme === theme.id}
                              onChange={(e) => handleSettingChange('preferences', 'theme', e.target.value)}
                              className="sr-only"
                            />
                            <div className={`p-4 border-2 rounded-lg ${
                              settings.preferences.theme === theme.id
                                ? 'border-green-500 bg-green-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}>
                              <div className={`w-full h-16 rounded-lg mb-2 ${theme.preview}`}></div>
                              <div className="text-center font-medium text-gray-900">{theme.name}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Color Scheme */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Color Scheme</label>
                      <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                        {colorSchemes.map((scheme) => (
                          <label key={scheme.id} className="cursor-pointer">
                            <input
                              type="radio"
                              name="colorScheme"
                              value={scheme.id}
                              checked={settings.preferences.colorScheme === scheme.id}
                              onChange={(e) => handleSettingChange('preferences', 'colorScheme', e.target.value)}
                              className="sr-only"
                            />
                            <div className={`p-3 border-2 rounded-lg text-center ${
                              settings.preferences.colorScheme === scheme.id
                                ? 'border-gray-900'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}>
                              <div className={`w-8 h-8 ${scheme.color} rounded-full mx-auto mb-2`}></div>
                              <div className="text-sm font-medium text-gray-900">{scheme.name}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Interface Options */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Interface Options</label>
                      <div className="space-y-3">
                        {[
                          { key: 'animations', label: 'Enable Animations', desc: 'Show smooth transitions and animations' },
                          { key: 'soundEffects', label: 'Sound Effects', desc: 'Play sounds for interactions and notifications' },
                          { key: 'hapticFeedback', label: 'Haptic Feedback', desc: 'Vibration feedback on mobile devices' },
                          { key: 'autoSave', label: 'Auto Save', desc: 'Automatically save your progress' }
                        ].map((setting) => (
                          <div key={setting.key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                            <div>
                              <div className="font-medium text-gray-900">{setting.label}</div>
                              <div className="text-sm text-gray-600">{setting.desc}</div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                checked={settings.preferences[setting.key]}
                                onChange={(e) => handleSettingChange('preferences', setting.key, e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Font Size */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Font Size</label>
                      <select
                        value={settings.preferences.fontSize}
                        onChange={(e) => handleSettingChange('preferences', 'fontSize', e.target.value)}
                        className="input-eco max-w-xs"
                      >
                        <option value="small">Small</option>
                        <option value="medium">Medium</option>
                        <option value="large">Large</option>
                        <option value="extra-large">Extra Large</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Settings */}
            {activeSection === 'advanced' && (
              <div className="space-y-6">
                <div className="card-eco">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-gradient-eco rounded-xl flex items-center justify-center">
                      <span className="text-white text-xl">⚙️</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">Advanced Settings</h3>
                      <p className="text-gray-600">Developer options and advanced features</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Account Management */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">Account Management</label>
                      <div className="space-y-3">
                        <button
                          onClick={() => setShowExportModal(true)}
                          className="w-full p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <span className="text-2xl">📁</span>
                            <div>
                              <div className="font-medium text-gray-900">Export Your Data</div>
                              <div className="text-sm text-gray-600">Download all your data in JSON format</div>
                            </div>
                          </div>
                        </button>

                        <button
                          onClick={handleResetSettings}
                          className="w-full p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <span className="text-2xl">🔄</span>
                            <div>
                              <div className="font-medium text-gray-900">Reset All Settings</div>
                              <div className="text-sm text-gray-600">Restore all settings to default values</div>
                            </div>
                          </div>
                        </button>

                        <button
                          onClick={() => setShowDeleteModal(true)}
                          className="w-full p-4 text-left border border-red-200 rounded-lg hover:bg-red-50 transition-colors text-red-600"
                        >
                          <div className="flex items-center gap-3">
                            <span className="text-2xl">🗑️</span>
                            <div>
                              <div className="font-medium">Delete Account</div>
                              <div className="text-sm text-red-500">Permanently delete your account and all data</div>
                            </div>
                          </div>
                        </button>
                      </div>
                    </div>

                    {/* App Information */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">App Information</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-600">Version</div>
                          <div className="font-medium text-gray-900">1.0.0</div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-600">Last Updated</div>
                          <div className="font-medium text-gray-900">March 2024</div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-600">Build</div>
                          <div className="font-medium text-gray-900">2024.03.15</div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-600">Environment</div>
                          <div className="font-medium text-gray-900">Production</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Export Data Modal */}
        {showExportModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-md w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Export Your Data</h3>
                <button
                  onClick={() => setShowExportModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-4">
                <p className="text-gray-600">
                  Download all your GreenMate data including profile information, habits, achievements, and preferences.
                </p>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-blue-600">ℹ️</span>
                    <span className="font-medium text-blue-900">What's included:</span>
                  </div>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Profile information</li>
                    <li>• Habit tracking data</li>
                    <li>• Achievement progress</li>
                    <li>• App preferences</li>
                    <li>• Environmental impact stats</li>
                  </ul>
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowExportModal(false)}
                    className="flex-1 btn-secondary py-2"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleExportData}
                    className="flex-1 btn-eco py-2"
                  >
                    📁 Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Account Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-md w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-red-900">Delete Account</h3>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-red-600">⚠️</span>
                    <span className="font-medium text-red-900">Warning: This action cannot be undone</span>
                  </div>
                  <p className="text-sm text-red-800">
                    Deleting your account will permanently remove all your data, including:
                  </p>
                  <ul className="text-sm text-red-800 mt-2 space-y-1">
                    <li>• Profile and personal information</li>
                    <li>• All habit tracking data</li>
                    <li>• Achievements and progress</li>
                    <li>• Friends and social connections</li>
                    <li>• Environmental impact statistics</li>
                  </ul>
                </div>
                <p className="text-gray-600">
                  Are you sure you want to permanently delete your GreenMate account?
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="flex-1 btn-secondary py-2"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDeleteAccount}
                    disabled={isLoading}
                    className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
                  >
                    {isLoading ? '🗑️ Deleting...' : '🗑️ Delete Account'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;
