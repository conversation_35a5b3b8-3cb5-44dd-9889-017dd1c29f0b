import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { AuthContext } from '../contexts/AuthContext';
import {
  FaCog, FaUser, FaBell, FaShieldAlt, FaGlobe, FaPalette,
  FaLeaf, FaToggleOn, FaToggleOff, FaSave, FaTrash,
  FaMoon, FaSun, FaVolumeUp, FaVolumeOff, FaLock,
  FaEnvelope, FaPhone, FaEye, FaEyeSlash, FaDownload,
  FaUpload, FaRecycle, FaTree, FaWater, FaBolt
} from 'react-icons/fa';

const Settings = () => {
  const { user } = useContext(AuthContext);
  const [activeSection, setActiveSection] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);

  const [settings, setSettings] = useState({
    // Profile Settings
    profile: {
      name: user?.name || 'Eco Warrior',
      email: user?.email || '<EMAIL>',
      phone: '+****************',
      bio: 'Passionate about sustainable living and environmental conservation.',
      location: 'San Francisco, CA',
      timezone: 'America/Los_Angeles',
      language: 'en'
    },

    // Notification Settings
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      habitReminders: true,
      challengeUpdates: true,
      weeklyReports: true,
      achievementAlerts: true,
      communityUpdates: false,
      marketingEmails: false,
      soundEnabled: true,
      vibrationEnabled: true
    },

    // Privacy Settings
    privacy: {
      profileVisibility: 'public',
      showAchievements: true,
      showActivity: true,
      showStats: true,
      allowFriendRequests: true,
      showOnLeaderboard: true,
      dataSharing: false,
      analyticsOptIn: true
    },

    // Appearance Settings
    appearance: {
      theme: 'eco',
      darkMode: false,
      colorScheme: 'nature',
      fontSize: 'medium',
      animations: true,
      compactMode: false,
      highContrast: false
    },

    // Eco Settings
    eco: {
      carbonTracking: true,
      waterTracking: true,
      energyTracking: true,
      wasteTracking: true,
      autoSuggestions: true,
      impactReminders: true,
      goalNotifications: true,
      sustainabilityTips: true
    }
  });

  const settingSections = [
    { id: 'profile', label: 'Profile', icon: FaUser, color: 'text-eco-forest' },
    { id: 'notifications', label: 'Notifications', icon: FaBell, color: 'text-eco-mint' },
    { id: 'privacy', label: 'Privacy & Security', icon: FaShieldAlt, color: 'text-eco-sky' },
    { id: 'appearance', label: 'Appearance', icon: FaPalette, color: 'text-eco-earth' },
    { id: 'eco', label: 'Eco Preferences', icon: FaLeaf, color: 'text-eco-forest' }
  ];

  const handleSettingChange = (section, key, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    // Here you would typically save to backend
    setHasChanges(false);
    // Show success message
  };

  const handleReset = () => {
    // Reset to default values
    setHasChanges(false);
  };

  const ToggleSwitch = ({ enabled, onChange, label, description }) => (
    <div className="flex items-center justify-between p-lg bg-white bg-opacity-50 rounded-xl border border-neutral-200 hover-lift">
      <div className="flex-1">
        <div className="font-medium text-neutral-900 mb-xs">{label}</div>
        {description && (
          <div className="text-sm text-neutral-600">{description}</div>
        )}
      </div>
      <button
        onClick={() => onChange(!enabled)}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          enabled ? 'bg-eco-forest-500' : 'bg-neutral-300'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

  return (
    <div className="min-h-screen py-xl nature-texture">
      <div className="container mx-auto">
        {/* Settings Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-2xl"
        >
          <div className="flex items-center justify-center gap-md mb-lg">
            <div className="w-16 h-16 bg-gradient-nature rounded-xl flex items-center justify-center">
              <FaCog className="text-white text-2xl animate-wave-subtle" />
            </div>
            <h1 className="text-4xl font-bold font-display text-eco-forest">
              Settings
            </h1>
          </div>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Customize your GreenMate experience and manage your eco-friendly preferences.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-xl">
          {/* Settings Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-1"
          >
            <div className="card-eco sticky top-8">
              <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                Settings Menu
              </h3>
              <nav className="space-y-sm">
                {settingSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center gap-md p-md rounded-lg text-left transition-all ${
                      activeSection === section.id
                        ? 'bg-gradient-nature text-white shadow-eco-sm'
                        : 'text-neutral-600 hover:bg-neutral-50 hover:text-eco-forest'
                    }`}
                  >
                    <section.icon className={`text-lg ${
                      activeSection === section.id ? 'text-white' : section.color
                    }`} />
                    <span className="font-medium">{section.label}</span>
                  </button>
                ))}
              </nav>

              {hasChanges && (
                <div className="mt-xl pt-lg border-t border-neutral-200">
                  <div className="space-y-sm">
                    <button
                      onClick={handleSave}
                      className="btn-nature w-full btn-sm"
                    >
                      <FaSave />
                      <span>Save Changes</span>
                    </button>
                    <button
                      onClick={handleReset}
                      className="btn-outline w-full btn-sm"
                    >
                      <FaTrash />
                      <span>Reset</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Settings Content */}
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:col-span-3"
          >
            {/* Profile Settings */}
            {activeSection === 'profile' && (
              <div className="card-forest">
                <div className="flex items-center gap-md mb-xl">
                  <div className="w-12 h-12 bg-gradient-forest rounded-xl flex items-center justify-center">
                    <FaUser className="text-white text-xl" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">
                    Profile Settings
                  </h2>
                </div>

                <div className="space-y-xl">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-lg">
                    <div className="form-group">
                      <label className="form-label">Full Name</label>
                      <input
                        type="text"
                        value={settings.profile.name}
                        onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
                        className="input-field"
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Email Address</label>
                      <input
                        type="email"
                        value={settings.profile.email}
                        onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-lg">
                    <div className="form-group">
                      <label className="form-label">Phone Number</label>
                      <input
                        type="tel"
                        value={settings.profile.phone}
                        onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
                        className="input-field"
                      />
                    </div>
                    <div className="form-group">
                      <label className="form-label">Location</label>
                      <input
                        type="text"
                        value={settings.profile.location}
                        onChange={(e) => handleSettingChange('profile', 'location', e.target.value)}
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Bio</label>
                    <textarea
                      value={settings.profile.bio}
                      onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
                      className="input-field h-24 resize-none"
                      placeholder="Tell us about your eco journey..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-lg">
                    <div className="form-group">
                      <label className="form-label">Timezone</label>
                      <select
                        value={settings.profile.timezone}
                        onChange={(e) => handleSettingChange('profile', 'timezone', e.target.value)}
                        className="input-field"
                      >
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/New_York">Eastern Time (ET)</option>
                      </select>
                    </div>
                    <div className="form-group">
                      <label className="form-label">Language</label>
                      <select
                        value={settings.profile.language}
                        onChange={(e) => handleSettingChange('profile', 'language', e.target.value)}
                        className="input-field"
                      >
                        <option value="en">English</option>
                        <option value="es">Español</option>
                        <option value="fr">Français</option>
                        <option value="de">Deutsch</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeSection === 'notifications' && (
              <div className="card-ocean">
                <div className="flex items-center gap-md mb-xl">
                  <div className="w-12 h-12 bg-gradient-ocean rounded-xl flex items-center justify-center">
                    <FaBell className="text-white text-xl animate-wave-subtle" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">
                    Notification Preferences
                  </h2>
                </div>

                <div className="space-y-lg">
                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      General Notifications
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.notifications.emailNotifications}
                        onChange={(value) => handleSettingChange('notifications', 'emailNotifications', value)}
                        label="Email Notifications"
                        description="Receive notifications via email"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.pushNotifications}
                        onChange={(value) => handleSettingChange('notifications', 'pushNotifications', value)}
                        label="Push Notifications"
                        description="Receive push notifications on your device"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.soundEnabled}
                        onChange={(value) => handleSettingChange('notifications', 'soundEnabled', value)}
                        label="Sound Effects"
                        description="Play sounds for notifications and interactions"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Eco Activity Notifications
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.notifications.habitReminders}
                        onChange={(value) => handleSettingChange('notifications', 'habitReminders', value)}
                        label="Habit Reminders"
                        description="Daily reminders for your eco habits"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.challengeUpdates}
                        onChange={(value) => handleSettingChange('notifications', 'challengeUpdates', value)}
                        label="Challenge Updates"
                        description="Updates on community challenges and competitions"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.achievementAlerts}
                        onChange={(value) => handleSettingChange('notifications', 'achievementAlerts', value)}
                        label="Achievement Alerts"
                        description="Notifications when you unlock new achievements"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.weeklyReports}
                        onChange={(value) => handleSettingChange('notifications', 'weeklyReports', value)}
                        label="Weekly Reports"
                        description="Weekly summary of your environmental impact"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Community & Marketing
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.notifications.communityUpdates}
                        onChange={(value) => handleSettingChange('notifications', 'communityUpdates', value)}
                        label="Community Updates"
                        description="Updates from the GreenMate community"
                      />
                      <ToggleSwitch
                        enabled={settings.notifications.marketingEmails}
                        onChange={(value) => handleSettingChange('notifications', 'marketingEmails', value)}
                        label="Marketing Emails"
                        description="Promotional emails and product updates"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Privacy Settings */}
            {activeSection === 'privacy' && (
              <div className="card-earth">
                <div className="flex items-center gap-md mb-xl">
                  <div className="w-12 h-12 bg-gradient-earth rounded-xl flex items-center justify-center">
                    <FaShieldAlt className="text-white text-xl animate-breathe" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">
                    Privacy & Security
                  </h2>
                </div>

                <div className="space-y-xl">
                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Profile Visibility
                    </h3>
                    <div className="space-y-md">
                      <div className="form-group">
                        <label className="form-label">Who can see your profile?</label>
                        <select
                          value={settings.privacy.profileVisibility}
                          onChange={(e) => handleSettingChange('privacy', 'profileVisibility', e.target.value)}
                          className="input-field"
                        >
                          <option value="public">Everyone</option>
                          <option value="friends">Friends only</option>
                          <option value="private">Only me</option>
                        </select>
                      </div>

                      <ToggleSwitch
                        enabled={settings.privacy.showAchievements}
                        onChange={(value) => handleSettingChange('privacy', 'showAchievements', value)}
                        label="Show Achievements"
                        description="Display your eco achievements on your profile"
                      />
                      <ToggleSwitch
                        enabled={settings.privacy.showActivity}
                        onChange={(value) => handleSettingChange('privacy', 'showActivity', value)}
                        label="Show Activity"
                        description="Display your recent eco activities"
                      />
                      <ToggleSwitch
                        enabled={settings.privacy.showStats}
                        onChange={(value) => handleSettingChange('privacy', 'showStats', value)}
                        label="Show Statistics"
                        description="Display your environmental impact statistics"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Social Features
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.privacy.allowFriendRequests}
                        onChange={(value) => handleSettingChange('privacy', 'allowFriendRequests', value)}
                        label="Allow Friend Requests"
                        description="Let other users send you friend requests"
                      />
                      <ToggleSwitch
                        enabled={settings.privacy.showOnLeaderboard}
                        onChange={(value) => handleSettingChange('privacy', 'showOnLeaderboard', value)}
                        label="Show on Leaderboard"
                        description="Appear on community leaderboards and rankings"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Data & Analytics
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.privacy.dataSharing}
                        onChange={(value) => handleSettingChange('privacy', 'dataSharing', value)}
                        label="Data Sharing"
                        description="Share anonymized data to help improve sustainability research"
                      />
                      <ToggleSwitch
                        enabled={settings.privacy.analyticsOptIn}
                        onChange={(value) => handleSettingChange('privacy', 'analyticsOptIn', value)}
                        label="Analytics"
                        description="Help us improve GreenMate with usage analytics"
                      />
                    </div>
                  </div>

                  <div className="bg-warning bg-opacity-10 border border-warning border-opacity-30 rounded-xl p-lg">
                    <div className="flex items-start gap-md">
                      <FaLock className="text-warning text-xl mt-xs" />
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-sm">
                          Account Security
                        </h4>
                        <p className="text-sm text-neutral-600 mb-md">
                          Keep your account secure with two-factor authentication and regular password updates.
                        </p>
                        <div className="flex gap-md">
                          <button className="btn-outline btn-sm">
                            Change Password
                          </button>
                          <button className="btn-outline btn-sm">
                            Enable 2FA
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeSection === 'appearance' && (
              <div className="card-eco">
                <div className="flex items-center gap-md mb-xl">
                  <div className="w-12 h-12 bg-gradient-nature rounded-xl flex items-center justify-center">
                    <FaPalette className="text-white text-xl animate-float-gentle" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">
                    Appearance & Display
                  </h2>
                </div>

                <div className="space-y-xl">
                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Theme & Colors
                    </h3>
                    <div className="space-y-md">
                      <div className="form-group">
                        <label className="form-label">Color Scheme</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-md">
                          {[
                            { id: 'nature', name: 'Nature', colors: ['bg-eco-forest-500', 'bg-eco-mint-500', 'bg-eco-sky-500'] },
                            { id: 'forest', name: 'Forest', colors: ['bg-eco-forest-600', 'bg-eco-forest-400', 'bg-eco-forest-200'] },
                            { id: 'ocean', name: 'Ocean', colors: ['bg-eco-sky-600', 'bg-eco-mint-500', 'bg-eco-sky-200'] },
                            { id: 'earth', name: 'Earth', colors: ['bg-eco-earth-600', 'bg-eco-earth-400', 'bg-eco-earth-200'] }
                          ].map((scheme) => (
                            <button
                              key={scheme.id}
                              onClick={() => handleSettingChange('appearance', 'colorScheme', scheme.id)}
                              className={`p-md rounded-xl border-2 transition-all ${
                                settings.appearance.colorScheme === scheme.id
                                  ? 'border-eco-forest-500 bg-eco-forest-50'
                                  : 'border-neutral-200 hover:border-neutral-300'
                              }`}
                            >
                              <div className="flex gap-xs mb-sm">
                                {scheme.colors.map((color, index) => (
                                  <div key={index} className={`w-6 h-6 rounded-full ${color}`}></div>
                                ))}
                              </div>
                              <div className="text-sm font-medium">{scheme.name}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      <ToggleSwitch
                        enabled={settings.appearance.darkMode}
                        onChange={(value) => handleSettingChange('appearance', 'darkMode', value)}
                        label="Dark Mode"
                        description="Use dark theme for better viewing in low light"
                      />
                      <ToggleSwitch
                        enabled={settings.appearance.highContrast}
                        onChange={(value) => handleSettingChange('appearance', 'highContrast', value)}
                        label="High Contrast"
                        description="Increase contrast for better accessibility"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Display Options
                    </h3>
                    <div className="space-y-md">
                      <div className="form-group">
                        <label className="form-label">Font Size</label>
                        <select
                          value={settings.appearance.fontSize}
                          onChange={(e) => handleSettingChange('appearance', 'fontSize', e.target.value)}
                          className="input-field"
                        >
                          <option value="small">Small</option>
                          <option value="medium">Medium</option>
                          <option value="large">Large</option>
                          <option value="extra-large">Extra Large</option>
                        </select>
                      </div>

                      <ToggleSwitch
                        enabled={settings.appearance.animations}
                        onChange={(value) => handleSettingChange('appearance', 'animations', value)}
                        label="Animations"
                        description="Enable smooth animations and transitions"
                      />
                      <ToggleSwitch
                        enabled={settings.appearance.compactMode}
                        onChange={(value) => handleSettingChange('appearance', 'compactMode', value)}
                        label="Compact Mode"
                        description="Use more compact layout to fit more content"
                      />
                    </div>
                  </div>

                  <div className="bg-info bg-opacity-10 border border-info border-opacity-30 rounded-xl p-lg">
                    <div className="flex items-start gap-md">
                      <FaPalette className="text-info text-xl mt-xs" />
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-sm">
                          Eco-Friendly Design
                        </h4>
                        <p className="text-sm text-neutral-600">
                          Our design system is inspired by nature and optimized for sustainability.
                          All animations are performance-optimized to reduce energy consumption.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Eco Preferences */}
            {activeSection === 'eco' && (
              <div className="card-forest">
                <div className="flex items-center gap-md mb-xl">
                  <div className="w-12 h-12 bg-gradient-forest rounded-xl flex items-center justify-center">
                    <FaLeaf className="text-white text-xl animate-breathe" />
                  </div>
                  <h2 className="text-2xl font-bold text-neutral-900 font-display">
                    Eco Preferences
                  </h2>
                </div>

                <div className="space-y-xl">
                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Environmental Tracking
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-md">
                      <ToggleSwitch
                        enabled={settings.eco.carbonTracking}
                        onChange={(value) => handleSettingChange('eco', 'carbonTracking', value)}
                        label="Carbon Footprint"
                        description="Track your CO₂ emissions and reductions"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.waterTracking}
                        onChange={(value) => handleSettingChange('eco', 'waterTracking', value)}
                        label="Water Usage"
                        description="Monitor your water consumption and savings"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.energyTracking}
                        onChange={(value) => handleSettingChange('eco', 'energyTracking', value)}
                        label="Energy Consumption"
                        description="Track your energy usage and efficiency"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.wasteTracking}
                        onChange={(value) => handleSettingChange('eco', 'wasteTracking', value)}
                        label="Waste Management"
                        description="Monitor recycling and waste reduction"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Smart Features
                    </h3>
                    <div className="space-y-md">
                      <ToggleSwitch
                        enabled={settings.eco.autoSuggestions}
                        onChange={(value) => handleSettingChange('eco', 'autoSuggestions', value)}
                        label="Auto Suggestions"
                        description="Get personalized eco-friendly recommendations"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.impactReminders}
                        onChange={(value) => handleSettingChange('eco', 'impactReminders', value)}
                        label="Impact Reminders"
                        description="Regular reminders about your environmental impact"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.goalNotifications}
                        onChange={(value) => handleSettingChange('eco', 'goalNotifications', value)}
                        label="Goal Notifications"
                        description="Alerts when you reach sustainability milestones"
                      />
                      <ToggleSwitch
                        enabled={settings.eco.sustainabilityTips}
                        onChange={(value) => handleSettingChange('eco', 'sustainabilityTips', value)}
                        label="Daily Tips"
                        description="Receive daily sustainability tips and advice"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-lg mb-lg text-neutral-900">
                      Impact Visualization
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-lg">
                      <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                        <FaTree className="text-2xl text-eco-forest mx-auto mb-md animate-wave-subtle" />
                        <div className="text-lg font-bold text-neutral-900">47</div>
                        <div className="text-sm text-neutral-600">Trees Saved</div>
                      </div>
                      <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                        <FaWater className="text-2xl text-eco-sky mx-auto mb-md animate-float-gentle" />
                        <div className="text-lg font-bold text-neutral-900">850L</div>
                        <div className="text-sm text-neutral-600">Water Saved</div>
                      </div>
                      <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                        <FaBolt className="text-2xl text-eco-earth mx-auto mb-md animate-pulse-eco" />
                        <div className="text-lg font-bold text-neutral-900">340kWh</div>
                        <div className="text-sm text-neutral-600">Energy Saved</div>
                      </div>
                      <div className="text-center p-lg bg-white bg-opacity-50 rounded-xl">
                        <FaRecycle className="text-2xl text-eco-mint mx-auto mb-md animate-breathe" />
                        <div className="text-lg font-bold text-neutral-900">1,247</div>
                        <div className="text-sm text-neutral-600">Items Recycled</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-success bg-opacity-10 border border-success border-opacity-30 rounded-xl p-lg">
                    <div className="flex items-start gap-md">
                      <FaLeaf className="text-success text-xl mt-xs animate-wave-subtle" />
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-sm">
                          Your Eco Impact
                        </h4>
                        <p className="text-sm text-neutral-600 mb-md">
                          You've made a significant positive impact on the environment!
                          Keep up the great work and inspire others to join the movement.
                        </p>
                        <div className="flex gap-md">
                          <button className="btn-nature btn-sm">
                            <FaDownload />
                            <span>Download Report</span>
                          </button>
                          <button className="btn-outline btn-sm">
                            <FaUpload />
                            <span>Share Impact</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Settings;