import axios from 'axios';

class MapService {
  constructor() {
    this.overpassUrl = 'https://overpass-api.de/api/interpreter';
    this.nominatimUrl = 'https://nominatim.openstreetmap.org';
  }

  // Get real eco-friendly places using Overpass API (OpenStreetMap data)
  async getEcoFriendlyPlaces(lat, lng, radius = 5000) {
    try {
      const queries = {
        ev_charging: `
          [out:json][timeout:25];
          (
            node["amenity"="charging_station"](around:${radius},${lat},${lng});
            way["amenity"="charging_station"](around:${radius},${lat},${lng});
            relation["amenity"="charging_station"](around:${radius},${lat},${lng});
          );
          out geom;
        `,
        recycling: `
          [out:json][timeout:25];
          (
            node["amenity"="recycling"](around:${radius},${lat},${lng});
            node["amenity"="waste_disposal"](around:${radius},${lat},${lng});
            way["amenity"="recycling"](around:${radius},${lat},${lng});
          );
          out geom;
        `,
        organic_shops: `
          [out:json][timeout:25];
          (
            node["shop"="organic"](around:${radius},${lat},${lng});
            node["shop"="health_food"](around:${radius},${lat},${lng});
            node["shop"="farm"](around:${radius},${lat},${lng});
            way["shop"="organic"](around:${radius},${lat},${lng});
          );
          out geom;
        `,
        renewable_energy: `
          [out:json][timeout:25];
          (
            node["power"="generator"]["generator:source"~"solar|wind|hydro"](around:${radius},${lat},${lng});
            way["power"="generator"]["generator:source"~"solar|wind|hydro"](around:${radius},${lat},${lng});
          );
          out geom;
        `,
        bike_sharing: `
          [out:json][timeout:25];
          (
            node["amenity"="bicycle_rental"](around:${radius},${lat},${lng});
            node["amenity"="bicycle_parking"](around:${radius},${lat},${lng});
            way["amenity"="bicycle_rental"](around:${radius},${lat},${lng});
          );
          out geom;
        `,
        farmers_markets: `
          [out:json][timeout:25];
          (
            node["amenity"="marketplace"](around:${radius},${lat},${lng});
            node["shop"="farm"](around:${radius},${lat},${lng});
            way["amenity"="marketplace"](around:${radius},${lat},${lng});
          );
          out geom;
        `
      };

      const allPlaces = [];

      // Fetch data for each category
      for (const [category, query] of Object.entries(queries)) {
        try {
          const response = await axios.post(this.overpassUrl, query, {
            headers: { 'Content-Type': 'text/plain' },
            timeout: 10000
          });

          const places = this.parseOverpassResponse(response.data, category);
          allPlaces.push(...places);
        } catch (error) {
          console.warn(`Failed to fetch ${category}:`, error.message);
        }
      }

      return allPlaces;
    } catch (error) {
      console.error('Error fetching eco-friendly places:', error);
      return [];
    }
  }

  parseOverpassResponse(data, category) {
    if (!data.elements) return [];

    return data.elements.map(element => {
      let lat, lng, name, address;

      // Handle different element types (node, way, relation)
      if (element.type === 'node') {
        lat = element.lat;
        lng = element.lon;
      } else if (element.type === 'way' && element.geometry) {
        // Use center of way
        const coords = element.geometry;
        lat = coords.reduce((sum, coord) => sum + coord.lat, 0) / coords.length;
        lng = coords.reduce((sum, coord) => sum + coord.lon, 0) / coords.length;
      } else {
        return null;
      }

      // Extract name and address
      const tags = element.tags || {};
      name = tags.name || tags.operator || tags.brand || this.getDefaultName(category);
      address = this.buildAddress(tags);

      return {
        id: `osm_${element.type}_${element.id}`,
        name,
        type: this.mapCategoryToType(category),
        coordinates: [lng, lat],
        address: {
          formatted: address,
          city: tags['addr:city'] || '',
          country: tags['addr:country'] || ''
        },
        description: this.getDescription(category, tags),
        tags: this.extractTags(tags),
        source: 'openstreetmap',
        realTime: true,
        contact: {
          phone: tags.phone || tags['contact:phone'] || '',
          website: tags.website || tags['contact:website'] || '',
          email: tags.email || tags['contact:email'] || ''
        },
        hours: tags.opening_hours || '',
        amenities: this.extractAmenities(tags)
      };
    }).filter(place => place !== null);
  }

  mapCategoryToType(category) {
    const mapping = {
      ev_charging: 'ev_charging',
      recycling: 'recycling_center',
      organic_shops: 'organic_shop',
      renewable_energy: 'renewable_energy',
      bike_sharing: 'sustainable_transport',
      farmers_markets: 'farmers_market'
    };
    return mapping[category] || 'sustainable_business';
  }

  getDefaultName(category) {
    const defaults = {
      ev_charging: 'EV Charging Station',
      recycling: 'Recycling Point',
      organic_shops: 'Organic Store',
      renewable_energy: 'Renewable Energy Site',
      bike_sharing: 'Bike Station',
      farmers_markets: 'Local Market'
    };
    return defaults[category] || 'Eco-Friendly Place';
  }

  buildAddress(tags) {
    const parts = [];
    if (tags['addr:housenumber']) parts.push(tags['addr:housenumber']);
    if (tags['addr:street']) parts.push(tags['addr:street']);
    if (tags['addr:city']) parts.push(tags['addr:city']);
    if (tags['addr:postcode']) parts.push(tags['addr:postcode']);
    
    return parts.length > 0 ? parts.join(', ') : 'Address not available';
  }

  getDescription(category, tags) {
    const descriptions = {
      ev_charging: `Electric vehicle charging station${tags.operator ? ` operated by ${tags.operator}` : ''}`,
      recycling: `Recycling facility${tags.recycling_type ? ` for ${tags.recycling_type}` : ''}`,
      organic_shops: `Organic and health food store${tags.organic ? ' with certified organic products' : ''}`,
      renewable_energy: `Renewable energy installation${tags['generator:source'] ? ` (${tags['generator:source']})` : ''}`,
      bike_sharing: `Bicycle sharing and parking facility${tags.capacity ? ` with ${tags.capacity} spaces` : ''}`,
      farmers_markets: `Local farmers market${tags.organic ? ' featuring organic produce' : ''}`
    };
    return descriptions[category] || 'Eco-friendly establishment';
  }

  extractTags(tags) {
    const ecoTags = [];
    if (tags.organic === 'yes') ecoTags.push('organic');
    if (tags.fair_trade === 'yes') ecoTags.push('fair-trade');
    if (tags.renewable === 'yes') ecoTags.push('renewable');
    if (tags.bicycle === 'yes') ecoTags.push('bike-friendly');
    if (tags.electric === 'yes') ecoTags.push('electric');
    if (tags.solar === 'yes') ecoTags.push('solar-powered');
    return ecoTags;
  }

  extractAmenities(tags) {
    const amenities = [];
    if (tags.wheelchair === 'yes') amenities.push('wheelchair_accessible');
    if (tags.parking) amenities.push('parking');
    if (tags.wifi === 'yes') amenities.push('wifi');
    if (tags.toilets === 'yes') amenities.push('restrooms');
    return amenities;
  }

  // Get user's current location
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy
          });
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  // Search for places by name/address
  async searchPlaces(query, lat, lng) {
    try {
      const response = await axios.get(`${this.nominatimUrl}/search`, {
        params: {
          q: query,
          format: 'json',
          limit: 10,
          lat: lat,
          lon: lng,
          bounded: 1,
          viewbox: `${lng-0.1},${lat-0.1},${lng+0.1},${lat+0.1}`
        }
      });

      return response.data.map(place => ({
        id: `nominatim_${place.place_id}`,
        name: place.display_name.split(',')[0],
        coordinates: [parseFloat(place.lon), parseFloat(place.lat)],
        address: {
          formatted: place.display_name,
          city: place.address?.city || place.address?.town || '',
          country: place.address?.country || ''
        },
        type: 'search_result',
        source: 'nominatim'
      }));
    } catch (error) {
      console.error('Search error:', error);
      return [];
    }
  }

  // Calculate distance between two points
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  // Get directions URL
  getDirectionsUrl(fromLat, fromLng, toLat, toLng) {
    return `https://www.openstreetmap.org/directions?engine=fossgis_osrm_car&route=${fromLat}%2C${fromLng}%3B${toLat}%2C${toLng}`;
  }
}

export default new MapService();
