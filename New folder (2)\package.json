{"name": "greenmate", "version": "1.0.0", "description": "Your Personal Eco-Friendly Planner", "main": "server/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["eco-friendly", "sustainability", "habits", "environment"], "author": "cognitodevlopers", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}