const mongoose = require('mongoose');

const challengeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Challenge title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Challenge description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    required: true,
    enum: ['transportation', 'energy', 'water', 'waste', 'food', 'lifestyle', 'community']
  },
  type: {
    type: String,
    enum: ['individual', 'community', 'global'],
    default: 'community'
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  duration: {
    type: Number, // in days
    required: true,
    min: 1,
    max: 365
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  goals: {
    targetParticipants: { type: Number, default: 100 },
    targetCompletions: { type: Number, default: 50 },
    targetImpact: {
      co2Saved: { type: Number, default: 0 },
      waterSaved: { type: Number, default: 0 },
      electricitySaved: { type: Number, default: 0 },
      plasticAvoided: { type: Number, default: 0 }
    }
  },
  rewards: {
    points: { type: Number, default: 50 },
    badge: {
      name: String,
      icon: String,
      description: String
    }
  },
  rules: [String],
  habits: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Habit'
  }],
  participants: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    joinedAt: { type: Date, default: Date.now },
    progress: { type: Number, default: 0 },
    completed: { type: Boolean, default: false },
    completedAt: Date
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  translations: {
    ta: {
      title: String,
      description: String
    },
    tanglish: {
      title: String,
      description: String
    }
  },
  statistics: {
    totalParticipants: { type: Number, default: 0 },
    completedParticipants: { type: Number, default: 0 },
    totalImpact: {
      co2Saved: { type: Number, default: 0 },
      waterSaved: { type: Number, default: 0 },
      electricitySaved: { type: Number, default: 0 },
      plasticAvoided: { type: Number, default: 0 }
    }
  }
}, {
  timestamps: true
});

// Indexes
challengeSchema.index({ startDate: 1, endDate: 1 });
challengeSchema.index({ category: 1 });
challengeSchema.index({ isActive: 1 });
challengeSchema.index({ featured: -1 });

// Virtual for active status
challengeSchema.virtual('isCurrentlyActive').get(function() {
  const now = new Date();
  return this.isActive && now >= this.startDate && now <= this.endDate;
});

// Method to join challenge
challengeSchema.methods.addParticipant = function(userId) {
  const existingParticipant = this.participants.find(p => p.user.toString() === userId.toString());
  if (existingParticipant) {
    return false; // Already joined
  }
  
  this.participants.push({ user: userId });
  this.statistics.totalParticipants += 1;
  return true;
};

// Method to update participant progress
challengeSchema.methods.updateParticipantProgress = function(userId, progress) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  if (participant) {
    participant.progress = progress;
    if (progress >= 100 && !participant.completed) {
      participant.completed = true;
      participant.completedAt = new Date();
      this.statistics.completedParticipants += 1;
    }
    return true;
  }
  return false;
};

// Static method to get active challenges
challengeSchema.statics.getActiveChallenges = function() {
  const now = new Date();
  return this.find({
    isActive: true,
    startDate: { $lte: now },
    endDate: { $gte: now }
  }).populate('createdBy', 'name avatar')
    .populate('habits', 'name icon category')
    .sort({ featured: -1, createdAt: -1 });
};

// Static method to get user's challenges
challengeSchema.statics.getUserChallenges = function(userId) {
  return this.find({
    'participants.user': userId
  }).populate('habits', 'name icon category');
};

module.exports = mongoose.model('Challenge', challengeSchema);
