const mongoose = require('mongoose');

const ecoPlaceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Place name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  type: {
    type: String,
    required: true,
    enum: [
      'recycling_center',
      'organic_shop',
      'ev_charging',
      'renewable_energy',
      'sustainable_business',
      'community_garden',
      'farmers_market',
      'repair_cafe',
      'zero_waste_store',
      'composting_facility'
    ]
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true,
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates'
      }
    }
  },
  address: {
    street: String,
    city: String,
    state: String,
    country: String,
    postalCode: String,
    formatted: String
  },
  contact: {
    phone: String,
    email: String,
    website: String
  },
  hours: {
    monday: { open: String, close: String, closed: { type: Boolean, default: false } },
    tuesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    wednesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    thursday: { open: String, close: String, closed: { type: Boolean, default: false } },
    friday: { open: String, close: String, closed: { type: Boolean, default: false } },
    saturday: { open: String, close: String, closed: { type: Boolean, default: false } },
    sunday: { open: String, close: String, closed: { type: Boolean, default: false } }
  },
  amenities: [String],
  services: [String],
  rating: {
    average: { type: Number, min: 0, max: 5, default: 0 },
    count: { type: Number, default: 0 }
  },
  reviews: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    rating: { type: Number, min: 1, max: 5, required: true },
    comment: { type: String, maxlength: 300 },
    createdAt: { type: Date, default: Date.now }
  }],
  images: [String],
  verified: {
    type: Boolean,
    default: false
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  visitCount: {
    type: Number,
    default: 0
  },
  favorites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  tags: [String],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create geospatial index for location-based queries
ecoPlaceSchema.index({ location: '2dsphere' });
ecoPlaceSchema.index({ type: 1 });
ecoPlaceSchema.index({ status: 1 });
ecoPlaceSchema.index({ verified: 1 });
ecoPlaceSchema.index({ 'rating.average': -1 });

// Method to add review
ecoPlaceSchema.methods.addReview = function(userId, rating, comment) {
  // Check if user already reviewed
  const existingReview = this.reviews.find(r => r.user.toString() === userId.toString());
  if (existingReview) {
    return false; // User already reviewed
  }

  this.reviews.push({
    user: userId,
    rating,
    comment
  });

  // Recalculate average rating
  this.calculateAverageRating();
  return true;
};

// Method to calculate average rating
ecoPlaceSchema.methods.calculateAverageRating = function() {
  if (this.reviews.length === 0) {
    this.rating.average = 0;
    this.rating.count = 0;
    return;
  }

  const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
  this.rating.average = Math.round((sum / this.reviews.length) * 10) / 10;
  this.rating.count = this.reviews.length;
};

// Method to toggle favorite
ecoPlaceSchema.methods.toggleFavorite = function(userId) {
  const index = this.favorites.indexOf(userId);
  if (index > -1) {
    this.favorites.splice(index, 1);
    return false; // Removed from favorites
  } else {
    this.favorites.push(userId);
    return true; // Added to favorites
  }
};

// Static method to find places near location
ecoPlaceSchema.statics.findNearby = function(longitude, latitude, maxDistance = 10000, type = null) {
  const query = {
    location: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance // in meters
      }
    },
    status: 'approved',
    isActive: true
  };

  if (type && type !== 'all') {
    query.type = type;
  }

  return this.find(query)
    .populate('submittedBy', 'name')
    .populate('reviews.user', 'name avatar')
    .limit(50);
};

// Static method to get user's favorite places
ecoPlaceSchema.statics.getUserFavorites = function(userId) {
  return this.find({
    favorites: userId,
    status: 'approved',
    isActive: true
  }).populate('submittedBy', 'name');
};

// Static method to get places by type
ecoPlaceSchema.statics.getByType = function(type) {
  return this.find({
    type,
    status: 'approved',
    isActive: true
  }).populate('submittedBy', 'name')
    .sort({ 'rating.average': -1, visitCount: -1 });
};

module.exports = mongoose.model('EcoPlace', ecoPlaceSchema);
