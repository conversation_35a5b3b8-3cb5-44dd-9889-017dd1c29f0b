const mongoose = require('mongoose');

const habitSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Habit name is required'],
    trim: true,
    maxlength: [100, 'Habit name cannot exceed 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  category: {
    type: String,
    required: true,
    enum: [
      'transportation',
      'energy',
      'water',
      'waste',
      'food',
      'shopping',
      'lifestyle',
      'nature',
      'community'
    ]
  },
  icon: {
    type: String,
    default: '🌱'
  },
  color: {
    type: String,
    default: '#10B981' // green-500
  },
  points: {
    type: Number,
    required: true,
    min: [1, 'Points must be at least 1'],
    max: [100, 'Points cannot exceed 100']
  },
  impact: {
    co2Saved: { type: Number, default: 0 }, // kg CO2
    waterSaved: { type: Number, default: 0 }, // liters
    electricitySaved: { type: Number, default: 0 }, // kWh
    plasticAvoided: { type: Number, default: 0 } // grams
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'easy'
  },
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    default: 'daily'
  },
  isDefault: {
    type: Boolean,
    default: false // true for system-created habits
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() {
      return !this.isDefault;
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  tags: [String],
  tips: [{
    text: String,
    language: {
      type: String,
      enum: ['en', 'ta', 'tanglish'],
      default: 'en'
    }
  }],
  translations: {
    ta: {
      name: String,
      description: String
    },
    tanglish: {
      name: String,
      description: String
    }
  },
  statistics: {
    totalCompletions: { type: Number, default: 0 },
    uniqueUsers: { type: Number, default: 0 },
    averageStreak: { type: Number, default: 0 }
  }
}, {
  timestamps: true
});

// Indexes for better performance
habitSchema.index({ category: 1 });
habitSchema.index({ isDefault: 1 });
habitSchema.index({ createdBy: 1 });
habitSchema.index({ 'statistics.totalCompletions': -1 });

// Virtual for localized name
habitSchema.virtual('localizedName').get(function() {
  return function(language = 'en') {
    if (language === 'ta' && this.translations.ta?.name) {
      return this.translations.ta.name;
    }
    if (language === 'tanglish' && this.translations.tanglish?.name) {
      return this.translations.tanglish.name;
    }
    return this.name;
  };
});

// Virtual for localized description
habitSchema.virtual('localizedDescription').get(function() {
  return function(language = 'en') {
    if (language === 'ta' && this.translations.ta?.description) {
      return this.translations.ta.description;
    }
    if (language === 'tanglish' && this.translations.tanglish?.description) {
      return this.translations.tanglish.description;
    }
    return this.description;
  };
});

// Method to get habit with localization
habitSchema.methods.toLocalizedJSON = function(language = 'en') {
  const obj = this.toObject();
  
  if (language === 'ta' && this.translations.ta) {
    obj.name = this.translations.ta.name || obj.name;
    obj.description = this.translations.ta.description || obj.description;
  } else if (language === 'tanglish' && this.translations.tanglish) {
    obj.name = this.translations.tanglish.name || obj.name;
    obj.description = this.translations.tanglish.description || obj.description;
  }
  
  return obj;
};

// Method to increment completion stats
habitSchema.methods.incrementStats = function() {
  this.statistics.totalCompletions += 1;
};

// Static method to get popular habits
habitSchema.statics.getPopularHabits = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ 'statistics.totalCompletions': -1 })
    .limit(limit);
};

// Static method to get habits by category
habitSchema.statics.getByCategory = function(category, language = 'en') {
  return this.find({ category, isActive: true })
    .sort({ 'statistics.totalCompletions': -1 });
};

// Static method to get default habits
habitSchema.statics.getDefaultHabits = function() {
  return this.find({ isDefault: true, isActive: true })
    .sort({ category: 1, points: 1 });
};

module.exports = mongoose.model('Habit', habitSchema);
