const mongoose = require('mongoose');

const habitTrackingSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  habit: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Habit',
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  completed: {
    type: Boolean,
    default: true
  },
  pointsEarned: {
    type: Number,
    required: true,
    min: 0
  },
  impact: {
    co2Saved: { type: Number, default: 0 },
    waterSaved: { type: Number, default: 0 },
    electricitySaved: { type: Number, default: 0 },
    plasticAvoided: { type: Number, default: 0 }
  },
  notes: {
    type: String,
    maxlength: [200, 'Notes cannot exceed 200 characters']
  },
  mood: {
    type: String,
    enum: ['excited', 'happy', 'neutral', 'motivated', 'proud'],
    default: 'happy'
  },
  difficulty: {
    type: Number,
    min: 1,
    max: 5,
    default: 3
  },
  streak: {
    type: Number,
    default: 1
  },
  isPartOfChallenge: {
    type: Boolean,
    default: false
  },
  challengeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Challenge'
  }
}, {
  timestamps: true
});

// Compound indexes for better query performance
habitTrackingSchema.index({ user: 1, date: -1 });
habitTrackingSchema.index({ user: 1, habit: 1, date: -1 });
habitTrackingSchema.index({ habit: 1, date: -1 });
habitTrackingSchema.index({ user: 1, completed: 1, date: -1 });

// Ensure one tracking entry per user per habit per day
habitTrackingSchema.index({ user: 1, habit: 1, date: 1 }, { unique: true });

// Virtual for formatted date
habitTrackingSchema.virtual('formattedDate').get(function() {
  return this.date.toISOString().split('T')[0];
});

// Static method to get user's tracking for a specific date
habitTrackingSchema.statics.getUserTrackingForDate = function(userId, date) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return this.find({
    user: userId,
    date: { $gte: startOfDay, $lte: endOfDay }
  }).populate('habit');
};

// Static method to get user's streak for a habit
habitTrackingSchema.statics.getUserHabitStreak = async function(userId, habitId) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let streak = 0;
  let currentDate = new Date(today);
  
  while (true) {
    const startOfDay = new Date(currentDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(currentDate);
    endOfDay.setHours(23, 59, 59, 999);
    
    const tracking = await this.findOne({
      user: userId,
      habit: habitId,
      date: { $gte: startOfDay, $lte: endOfDay },
      completed: true
    });
    
    if (tracking) {
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    } else {
      break;
    }
  }
  
  return streak;
};

// Static method to get user's weekly progress
habitTrackingSchema.statics.getWeeklyProgress = function(userId, startDate) {
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + 6);
  endDate.setHours(23, 59, 59, 999);
  
  return this.find({
    user: userId,
    date: { $gte: startDate, $lte: endDate }
  }).populate('habit');
};

// Static method to get user's monthly stats
habitTrackingSchema.statics.getMonthlyStats = function(userId, year, month) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0, 23, 59, 59, 999);
  
  return this.aggregate([
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        date: { $gte: startDate, $lte: endDate },
        completed: true
      }
    },
    {
      $group: {
        _id: null,
        totalCompletions: { $sum: 1 },
        totalPoints: { $sum: '$pointsEarned' },
        totalCO2Saved: { $sum: '$impact.co2Saved' },
        totalWaterSaved: { $sum: '$impact.waterSaved' },
        totalElectricitySaved: { $sum: '$impact.electricitySaved' },
        totalPlasticAvoided: { $sum: '$impact.plasticAvoided' },
        uniqueHabits: { $addToSet: '$habit' }
      }
    },
    {
      $project: {
        _id: 0,
        totalCompletions: 1,
        totalPoints: 1,
        totalCO2Saved: 1,
        totalWaterSaved: 1,
        totalElectricitySaved: 1,
        totalPlasticAvoided: 1,
        uniqueHabitsCount: { $size: '$uniqueHabits' }
      }
    }
  ]);
};

// Static method to get habit completion rate
habitTrackingSchema.statics.getHabitCompletionRate = async function(userId, habitId, days = 30) {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const totalDays = days;
  const completedDays = await this.countDocuments({
    user: userId,
    habit: habitId,
    date: { $gte: startDate, $lte: endDate },
    completed: true
  });
  
  return Math.round((completedDays / totalDays) * 100);
};

// Method to calculate points based on streak
habitTrackingSchema.methods.calculateStreakBonus = function() {
  let bonus = 0;
  if (this.streak >= 7) bonus += 5; // Weekly streak bonus
  if (this.streak >= 30) bonus += 15; // Monthly streak bonus
  if (this.streak >= 100) bonus += 30; // 100-day streak bonus
  return bonus;
};

module.exports = mongoose.model('HabitTracking', habitTrackingSchema);
