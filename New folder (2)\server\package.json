{"name": "greenmate-server", "version": "1.0.0", "description": "GreenMate Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedData.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["eco-friendly", "sustainability", "api", "nodejs"], "author": "cognitodevlopers", "license": "MIT"}