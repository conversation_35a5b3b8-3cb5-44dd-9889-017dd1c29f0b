const express = require('express');
const axios = require('axios');
const { auth } = require('../middleware/auth');
const geminiService = require('../services/geminiService');

const router = express.Router();

// @route   POST /api/ai/chat
// @desc    Chat with <PERSON> assistant using Gemini
// @access  Private
router.post('/chat', auth, async (req, res) => {
  try {
    const { message, context = {} } = req.body;

    if (!message || message.trim().length === 0) {
      return res.status(400).json({ message: 'Message is required' });
    }

    // Add user info to context
    const userContext = {
      ...context,
      userId: req.user.id,
      userLevel: req.user.ecoProfile?.level || 1,
      userPoints: req.user.ecoProfile?.totalPoints || 0
    };

    const response = await geminiService.generateEcoAdvice(message, userContext);

    res.json(response);

  } catch (error) {
    console.error('AI chat error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/ai/suggestions
// @desc    Get personalized AI suggestions
// @access  Private
router.get('/suggestions', auth, async (req, res) => {
  try {
    const userContext = {
      userId: req.user.id,
      userLevel: req.user.ecoProfile?.level || 1,
      userPoints: req.user.ecoProfile?.totalPoints || 0,
      userHabits: req.user.habits || []
    };

    const suggestions = await geminiService.getPersonalizedSuggestions(userContext);

    res.json({ suggestions });

  } catch (error) {
    console.error('AI suggestions error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/ai/feedback
// @desc    Submit feedback for AI responses
// @access  Private
router.post('/feedback', auth, async (req, res) => {
  try {
    const { messageId, rating, userId } = req.body;

    // In a real app, you'd save this feedback to improve the AI
    console.log('AI Feedback received:', { messageId, rating, userId });

    res.json({ message: 'Feedback received successfully' });

  } catch (error) {
    console.error('AI feedback error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/ai/ask
// @desc    Ask AI assistant for eco-friendly advice
// @access  Private
router.post('/ask', auth, async (req, res) => {
  try {
    const { question, language = 'en' } = req.body;

    if (!question || question.trim().length === 0) {
      return res.status(400).json({ message: 'Question is required' });
    }

    // For now, we'll provide predefined responses
    // In a real implementation, you'd integrate with Gemini API
    const response = await getEcoAdvice(question, language);

    res.json({
      question,
      answer: response.answer,
      tips: response.tips,
      language,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('AI ask error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/ai/suggestions
// @desc    Get AI-powered habit suggestions
// @access  Private
router.get('/suggestions', auth, async (req, res) => {
  try {
    const { category, language = 'en' } = req.query;

    const suggestions = getHabitSuggestions(category, language);

    res.json({
      suggestions,
      category: category || 'all',
      language
    });

  } catch (error) {
    console.error('AI suggestions error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/ai/analyze-impact
// @desc    Analyze user's environmental impact
// @access  Private
router.post('/analyze-impact', auth, async (req, res) => {
  try {
    const { habits, timeframe = 'month' } = req.body;

    const analysis = analyzeEnvironmentalImpact(habits, timeframe);

    res.json({
      analysis,
      timeframe,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('AI analyze impact error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to get eco advice (simulated AI responses)
async function getEcoAdvice(question, language) {
  const lowerQuestion = question.toLowerCase();

  // Predefined responses for common questions
  const responses = {
    en: {
      composting: {
        answer: "Composting is a great way to reduce waste and create nutrient-rich soil! Start with a simple bin in your backyard or even a small container for apartment composting.",
        tips: [
          "Add brown materials (dry leaves, paper) and green materials (food scraps, grass clippings) in a 3:1 ratio",
          "Turn your compost pile every 2-3 weeks to add oxygen",
          "Keep the pile moist but not soggy",
          "Avoid meat, dairy, and oily foods in your compost"
        ]
      },
      plastic: {
        answer: "Glass is generally better than plastic for the environment! Glass is 100% recyclable and doesn't leach chemicals into food or drinks.",
        tips: [
          "Glass can be recycled infinitely without losing quality",
          "Plastic takes 450+ years to decompose, glass is inert",
          "Choose glass containers for food storage when possible",
          "Support businesses that use glass packaging"
        ]
      },
      energy: {
        answer: "There are many ways to save energy at home! Small changes can make a big difference in your carbon footprint and electricity bills.",
        tips: [
          "Switch to LED bulbs - they use 75% less energy",
          "Unplug electronics when not in use",
          "Use a programmable thermostat",
          "Air dry clothes instead of using the dryer",
          "Seal air leaks around windows and doors"
        ]
      },
      transport: {
        answer: "Sustainable transportation options can significantly reduce your carbon footprint while often saving money too!",
        tips: [
          "Walk or bike for short trips under 2 miles",
          "Use public transportation when available",
          "Carpool or use ride-sharing for longer trips",
          "Consider electric or hybrid vehicles for your next car",
          "Work from home when possible to reduce commuting"
        ]
      }
    },
    ta: {
      composting: {
        answer: "உரமாக்கல் கழிவுகளை குறைக்கவும் ஊட்டச்சத்து நிறைந்த மண்ணை உருவாக்கவும் ஒரு சிறந்த வழியாகும்! உங்கள் வீட்டு முற்றத்தில் ஒரு எளிய தொட்டியுடன் அல்லது அடுக்குமாடி குடியிருப்புக்கு ஒரு சிறிய கொள்கலனுடன் தொடங்குங்கள்.",
        tips: [
          "பழுப்பு பொருட்கள் (உலர்ந்த இலைகள், காகிதம்) மற்றும் பச்சை பொருட்களை (உணவு கழிவுகள், புல் வெட்டுகள்) 3:1 விகிதத்தில் சேர்க்கவும்",
          "ஆக்ஸிஜன் சேர்க்க ஒவ்வொரு 2-3 வாரங்களுக்கும் உங்கள் உரக் குவியலைத் திருப்பவும்",
          "குவியலை ஈரமாக வைத்திருங்கள் ஆனால் நனைந்து போகாமல்",
          "உங்கள் உரத்தில் இறைச்சி, பால் பொருட்கள் மற்றும் எண்ணெய் உணவுகளைத் தவிர்க்கவும்"
        ]
      },
      plastic: {
        answer: "சுற்றுச்சூழலுக்கு பிளாஸ்டிக்கை விட கண்ணாடி பொதுவாக சிறந்தது! கண்ணாடி 100% மறுசுழற்சி செய்யக்கூடியது மற்றும் உணவு அல்லது பானங்களில் இரசாயனங்களை கசியவிடாது.",
        tips: [
          "கண்ணாடி தரம் இழக்காமல் எல்லையற்ற முறை மறுசுழற்சி செய்யப்படலாம்",
          "பிளாஸ்டிக் சிதைவதற்கு 450+ ஆண்டுகள் ஆகும், கண்ணாடி மாறாதது",
          "உணவு சேமிப்புக்கு முடிந்தவரை கண்ணாடி கொள்கலன்களைத் தேர்ந்தெடுக்கவும்",
          "கண்ணாடி பேக்கேஜிங் பயன்படுத்தும் வணிகங்களை ஆதரிக்கவும்"
        ]
      }
    },
    tanglish: {
      composting: {
        answer: "Composting waste reduce panna and nutrient-rich soil create panna oru best way! Unga backyard-la simple bin-oda start pannunga or apartment-ku small container use pannunga.",
        tips: [
          "Brown materials (dry leaves, paper) and green materials (food scraps, grass) 3:1 ratio-la add pannunga",
          "Oxygen add panna every 2-3 weeks-ku compost pile-a turn pannunga",
          "Pile-a moist-a vachukonga but soggy aaga koodadhu",
          "Unga compost-la meat, dairy, oily foods avoid pannunga"
        ]
      },
      plastic: {
        answer: "Environment-ku plastic-a vida glass generally better! Glass 100% recyclable and food or drinks-la chemicals leak pannadu.",
        tips: [
          "Glass quality lose pannama infinite times recycle panna mudiyum",
          "Plastic decompose aaga 450+ years aagum, glass inert",
          "Food storage-ku possible-na glass containers choose pannunga",
          "Glass packaging use panra businesses-a support pannunga"
        ]
      }
    }
  };

  // Determine response category based on keywords
  let category = 'general';
  if (lowerQuestion.includes('compost')) category = 'composting';
  else if (lowerQuestion.includes('plastic') || lowerQuestion.includes('glass')) category = 'plastic';
  else if (lowerQuestion.includes('energy') || lowerQuestion.includes('electricity')) category = 'energy';
  else if (lowerQuestion.includes('transport') || lowerQuestion.includes('car') || lowerQuestion.includes('travel')) category = 'transport';

  const langResponses = responses[language] || responses.en;
  const response = langResponses[category];

  if (response) {
    return response;
  }

  // Default response
  return {
    answer: language === 'ta'
      ? "இது ஒரு சிறந்த கேள்வி! சுற்றுச்சூழல் நட்பு வாழ்க்கை முறையைப் பற்றி மேலும் அறிய, எங்கள் பழக்க பிரிவு மற்றும் தினசரி குறிப்புகளைப் பார்க்கவும்."
      : language === 'tanglish'
      ? "Idhu oru great question! Eco-friendly lifestyle pathi more therinjukka, enga habits section and daily tips-a parunga."
      : "That's a great question! For more eco-friendly advice, check out our habits section and daily tips.",
    tips: [
      language === 'ta' ? "தினசரி சுற்றுச்சூழல் நட்பு பழக்கங்களைப் பின்பற்றுங்கள்" :
      language === 'tanglish' ? "Daily eco-friendly habits follow pannunga" : "Follow daily eco-friendly habits",
      language === 'ta' ? "உங்கள் தாக்கத்தைக் கண்காணிக்கவும்" :
      language === 'tanglish' ? "Unga impact-a track pannunga" : "Track your environmental impact",
      language === 'ta' ? "சமூக சவால்களில் பங்கேற்கவும்" :
      language === 'tanglish' ? "Community challenges-la participate pannunga" : "Participate in community challenges"
    ]
  };
}

// Helper function to get habit suggestions
function getHabitSuggestions(category, language) {
  const suggestions = {
    en: {
      transportation: [
        { habit: "Use public transport", impact: "Reduces CO₂ by 2.3kg per trip", difficulty: "easy" },
        { habit: "Walk or bike for short trips", impact: "Zero emissions + health benefits", difficulty: "medium" },
        { habit: "Carpool to work", impact: "Reduces CO₂ by 50% per person", difficulty: "easy" }
      ],
      energy: [
        { habit: "Switch to LED bulbs", impact: "75% less energy consumption", difficulty: "easy" },
        { habit: "Unplug devices when not in use", impact: "Saves 5-10% on electricity bill", difficulty: "easy" },
        { habit: "Use natural light during day", impact: "Reduces electricity usage", difficulty: "easy" }
      ],
      waste: [
        { habit: "Carry reusable bags", impact: "Eliminates 1000+ plastic bags/year", difficulty: "easy" },
        { habit: "Start composting", impact: "Reduces household waste by 30%", difficulty: "medium" },
        { habit: "Use refillable water bottle", impact: "Saves 1500+ plastic bottles/year", difficulty: "easy" }
      ]
    },
    ta: {
      transportation: [
        { habit: "பொது போக்குவரத்தைப் பயன்படுத்துங்கள்", impact: "ஒரு பயணத்திற்கு 2.3 கிலோ CO₂ குறைக்கிறது", difficulty: "எளிது" },
        { habit: "குறுகிய பயணங்களுக்கு நடக்கவும் அல்லது சைக்கிள் ஓட்டவும்", impact: "பூஜ்ஜிய உமிழ்வு + ஆரோக்கிய நன்மைகள்", difficulty: "நடுத்தர" }
      ],
      energy: [
        { habit: "LED விளக்குகளுக்கு மாறுங்கள்", impact: "75% குறைவான ஆற்றல் நுகர்வு", difficulty: "எளிது" },
        { habit: "பயன்படுத்தாதபோது சாதனங்களை துண்டிக்கவும்", impact: "மின் கட்டணத்தில் 5-10% சேமிப்பு", difficulty: "எளிது" }
      ]
    },
    tanglish: {
      transportation: [
        { habit: "Public transport use pannunga", impact: "Oru trip-ku 2.3kg CO₂ reduce aagum", difficulty: "easy" },
        { habit: "Short trips-ku walk or cycle pannunga", impact: "Zero emissions + health benefits", difficulty: "medium" }
      ],
      energy: [
        { habit: "LED bulbs-ku change pannunga", impact: "75% less energy consumption", difficulty: "easy" },
        { habit: "Use pannama irukum bodhu devices unplug pannunga", impact: "Electricity bill-la 5-10% save aagum", difficulty: "easy" }
      ]
    }
  };

  const langSuggestions = suggestions[language] || suggestions.en;

  if (category && langSuggestions[category]) {
    return langSuggestions[category];
  }

  // Return all suggestions if no specific category
  return Object.values(langSuggestions).flat();
}

// Helper function to analyze environmental impact
function analyzeEnvironmentalImpact(habits, timeframe) {
  // This is a simplified analysis - in a real app, you'd use actual user data
  const mockAnalysis = {
    summary: {
      co2Saved: 45.6,
      waterSaved: 234,
      electricitySaved: 12.3,
      plasticAvoided: 89,
      treesEquivalent: 2.1
    },
    trends: {
      improving: ["energy_usage", "waste_reduction"],
      stable: ["transportation"],
      needsAttention: ["water_usage"]
    },
    recommendations: [
      "Great job on reducing energy consumption! Keep it up.",
      "Consider carpooling more often to improve your transportation score.",
      "Try implementing water-saving habits like shorter showers."
    ],
    achievements: [
      "🌟 Eco Warrior - 30 days of consistent habits",
      "♻️ Waste Reducer - Avoided 50+ plastic items",
      "⚡ Energy Saver - 20% reduction in electricity usage"
    ]
  };

  return mockAnalysis;
}

module.exports = router;
