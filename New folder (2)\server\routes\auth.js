const express = require('express');
const { body, validationResult } = require('express-validator');
const { generateToken, auth } = require('../middleware/auth');

const router = express.Router();

// Check if we're in development mode without database
const isDevelopmentMode = process.env.NODE_ENV === 'development';
let User;

try {
  User = require('../models/User');
} catch (error) {
  console.warn('⚠️ User model not available, running in development mode');
  User = null;
}

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please enter a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, email, password } = req.body;

    // Development mode without database
    if (!User) {
      const mockUser = {
        id: 'dev_' + Date.now(),
        name,
        email,
        avatar: '🌱',
        ecoProfile: {
          level: 1,
          totalPoints: 0,
          streak: 0,
          impact: {
            co2Saved: 0,
            waterSaved: 0,
            electricitySaved: 0,
            plasticAvoided: 0,
            treesEquivalent: 0
          }
        },
        preferences: {
          notifications: true,
          privacy: 'public'
        },
        createdAt: new Date()
      };

      const token = generateToken(mockUser.id);

      return res.status(201).json({
        message: 'User registered successfully (Development Mode)',
        token,
        user: mockUser
      });
    }

    // Production mode with database
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        message: 'User already exists with this email'
      });
    }

    const user = new User({
      name,
      email,
      password
    });

    await user.save();

    const token = generateToken(user._id);

    const userData = {
      id: user._id,
      name: user.name,
      email: user.email,
      avatar: user.avatar,
      ecoProfile: user.ecoProfile,
      preferences: user.preferences,
      createdAt: user.createdAt
    };

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: userData
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      message: 'Server error during registration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please enter a valid email'),
  body('password')
    .exists()
    .withMessage('Password is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Development mode without database
    if (!User) {
      // Simple demo credentials
      if (email === '<EMAIL>' && password === 'demo123') {
        const mockUser = {
          id: 'demo_user',
          name: 'Demo User',
          email: '<EMAIL>',
          avatar: '🌱',
          bio: 'Welcome to GreenMate! This is a demo account showcasing our eco-friendly features.',
          location: 'San Francisco, CA',
          ecoProfile: {
            level: 5,
            totalPoints: 1250,
            streak: 15,
            impact: {
              co2Saved: 45.2,
              waterSaved: 320,
              electricitySaved: 125,
              plasticAvoided: 1200,
              treesEquivalent: 2.1
            }
          },
          preferences: {
            notifications: true,
            privacy: 'public'
          },
          social: {
            followingCount: 12,
            followersCount: 8,
            friendsCount: 5
          },
          lastActive: new Date(),
          createdAt: new Date('2024-01-01')
        };

        const token = generateToken(mockUser.id);

        return res.json({
          message: 'Login successful (Development Mode)',
          token,
          user: mockUser
        });
      } else {
        return res.status(400).json({
          message: 'Invalid credentials. Try <EMAIL> / demo123'
        });
      }
    }

    // Production mode with database
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({
        message: 'Invalid credentials'
      });
    }

    if (!user.isActive) {
      return res.status(400).json({
        message: 'Account is deactivated. Please contact support.'
      });
    }

    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({
        message: 'Invalid credentials'
      });
    }

    user.updateLastActive();
    await user.save();

    const token = generateToken(user._id);

    const userData = {
      id: user._id,
      name: user.name,
      email: user.email,
      avatar: user.avatar,
      bio: user.bio,
      location: user.location,
      ecoProfile: user.ecoProfile,
      preferences: user.preferences,
      social: {
        followingCount: user.social.following.length,
        followersCount: user.social.followers.length,
        friendsCount: user.social.friends.length
      },
      lastActive: user.lastActive,
      createdAt: user.createdAt
    };

    res.json({
      message: 'Login successful',
      token,
      user: userData
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      message: 'Server error during login',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    // Development mode without database
    if (!User) {
      const mockUser = {
        id: 'demo_user',
        name: 'Demo User',
        email: '<EMAIL>',
        avatar: '🌱',
        bio: 'Welcome to GreenMate! This is a demo account showcasing our eco-friendly features.',
        location: 'San Francisco, CA',
        ecoProfile: {
          level: 5,
          totalPoints: 1250,
          streak: 15,
          impact: {
            co2Saved: 45.2,
            waterSaved: 320,
            electricitySaved: 125,
            plasticAvoided: 1200,
            treesEquivalent: 2.1
          }
        },
        preferences: {
          notifications: true,
          privacy: 'public'
        },
        social: {
          followingCount: 12,
          followersCount: 8,
          friendsCount: 5
        },
        joinedChallenges: [],
        lastActive: new Date(),
        createdAt: new Date('2024-01-01')
      };

      return res.json({ user: mockUser });
    }

    // Production mode with database
    const user = await User.findById(req.user.id)
      .select('-password')
      .populate('social.following', 'name avatar ecoProfile.level')
      .populate('social.followers', 'name avatar ecoProfile.level')
      .populate('joinedChallenges.challengeId', 'name description endDate');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        bio: user.bio,
        location: user.location,
        ecoProfile: user.ecoProfile,
        preferences: user.preferences,
        social: user.social,
        joinedChallenges: user.joinedChallenges,
        lastActive: user.lastActive,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (client-side token removal)
// @access  Private
router.post('/logout', auth, async (req, res) => {
  try {
    // Update last active time
    req.user.updateLastActive();
    await req.user.save();

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: 'Server error during logout' });
  }
});

// @route   POST /api/auth/verify-token
// @desc    Verify if token is valid
// @access  Private
router.post('/verify-token', auth, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user._id,
      name: req.user.name,
      email: req.user.email
    }
  });
});

// Demo user is handled in development mode within the routes

module.exports = router;
