const express = require('express');
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const Challenge = require('../models/Challenge');
const Habit = require('../models/Habit');
const HabitTracking = require('../models/HabitTracking');

const router = express.Router();

// @route   GET /api/challenges
// @desc    Get active challenges
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { status = 'active', category, limit = 20 } = req.query;

    let query = { isActive: true };

    if (status === 'active') {
      const now = new Date();
      query.startDate = { $lte: now };
      query.endDate = { $gte: now };
    } else if (status === 'upcoming') {
      query.startDate = { $gt: new Date() };
    } else if (status === 'completed') {
      query.endDate = { $lt: new Date() };
    }

    if (category) {
      query.category = category;
    }

    const challenges = await Challenge.find(query)
      .populate('createdBy', 'name avatar')
      .populate('habits', 'name icon category points')
      .sort({ featured: -1, startDate: -1 })
      .limit(parseInt(limit));

    // Add user participation status
    const challengesWithStatus = challenges.map(challenge => {
      const userParticipation = challenge.participants.find(
        p => p.user.toString() === req.user.id
      );

      return {
        ...challenge.toObject(),
        userParticipating: !!userParticipation,
        userProgress: userParticipation ? userParticipation.progress : 0,
        userCompleted: userParticipation ? userParticipation.completed : false,
        daysRemaining: Math.max(0, Math.ceil((challenge.endDate - new Date()) / (1000 * 60 * 60 * 24)))
      };
    });

    res.json({
      challenges: challengesWithStatus,
      total: challenges.length
    });
  } catch (error) {
    console.error('Get challenges error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/challenges
// @desc    Create new challenge
// @access  Private
router.post('/', [
  auth,
  body('title').trim().isLength({ min: 5, max: 100 }).withMessage('Title must be 5-100 characters'),
  body('description').trim().isLength({ min: 10, max: 500 }).withMessage('Description must be 10-500 characters'),
  body('category').isIn(['transportation', 'energy', 'water', 'waste', 'food', 'lifestyle', 'community']),
  body('duration').isInt({ min: 1, max: 365 }).withMessage('Duration must be 1-365 days'),
  body('startDate').isISO8601().withMessage('Valid start date required'),
  body('habits').isArray({ min: 1 }).withMessage('At least one habit required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { title, description, category, duration, startDate, habits, type, difficulty } = req.body;

    // Validate habits exist
    const validHabits = await Habit.find({ _id: { $in: habits }, isActive: true });
    if (validHabits.length !== habits.length) {
      return res.status(400).json({ message: 'Some habits are invalid' });
    }

    const start = new Date(startDate);
    const end = new Date(start.getTime() + (duration * 24 * 60 * 60 * 1000));

    const challenge = new Challenge({
      title,
      description,
      category,
      type: type || 'community',
      difficulty: difficulty || 'medium',
      duration,
      startDate: start,
      endDate: end,
      habits,
      createdBy: req.user.id
    });

    await challenge.save();
    await challenge.populate('habits', 'name icon category points');

    res.status(201).json({
      message: 'Challenge created successfully',
      challenge
    });
  } catch (error) {
    console.error('Create challenge error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/challenges/:id/join
// @desc    Join a challenge
// @access  Private
router.post('/:id/join', auth, async (req, res) => {
  try {
    const challenge = await Challenge.findById(req.params.id);

    if (!challenge) {
      return res.status(404).json({ message: 'Challenge not found' });
    }

    if (!challenge.isCurrentlyActive) {
      return res.status(400).json({ message: 'Challenge is not currently active' });
    }

    const added = challenge.addParticipant(req.user.id);
    if (!added) {
      return res.status(400).json({ message: 'Already participating in this challenge' });
    }

    await challenge.save();

    res.json({
      message: 'Successfully joined the challenge!',
      participantCount: challenge.statistics.totalParticipants
    });
  } catch (error) {
    console.error('Join challenge error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/challenges/:id/leave
// @desc    Leave a challenge
// @access  Private
router.post('/:id/leave', auth, async (req, res) => {
  try {
    const challenge = await Challenge.findById(req.params.id);

    if (!challenge) {
      return res.status(404).json({ message: 'Challenge not found' });
    }

    const participantIndex = challenge.participants.findIndex(
      p => p.user.toString() === req.user.id
    );

    if (participantIndex === -1) {
      return res.status(400).json({ message: 'Not participating in this challenge' });
    }

    challenge.participants.splice(participantIndex, 1);
    challenge.statistics.totalParticipants = Math.max(0, challenge.statistics.totalParticipants - 1);

    await challenge.save();

    res.json({
      message: 'Successfully left the challenge',
      participantCount: challenge.statistics.totalParticipants
    });
  } catch (error) {
    console.error('Leave challenge error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/challenges/:id
// @desc    Get challenge details
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const challenge = await Challenge.findById(req.params.id)
      .populate('createdBy', 'name avatar ecoProfile.level')
      .populate('habits', 'name icon category points description')
      .populate('participants.user', 'name avatar ecoProfile.level');

    if (!challenge) {
      return res.status(404).json({ message: 'Challenge not found' });
    }

    const userParticipation = challenge.participants.find(
      p => p.user._id.toString() === req.user.id
    );

    // Calculate user's progress if participating
    let userProgress = 0;
    if (userParticipation) {
      const completedHabits = await HabitTracking.countDocuments({
        user: req.user.id,
        habit: { $in: challenge.habits.map(h => h._id) },
        date: { $gte: challenge.startDate, $lte: new Date() },
        completed: true
      });

      const totalRequired = challenge.habits.length * challenge.duration;
      userProgress = totalRequired > 0 ? Math.round((completedHabits / totalRequired) * 100) : 0;

      // Update progress in database
      if (userParticipation.progress !== userProgress) {
        challenge.updateParticipantProgress(req.user.id, userProgress);
        await challenge.save();
      }
    }

    // Get leaderboard for this challenge
    const leaderboard = challenge.participants
      .sort((a, b) => b.progress - a.progress)
      .slice(0, 10)
      .map((participant, index) => ({
        rank: index + 1,
        user: participant.user,
        progress: participant.progress,
        completed: participant.completed,
        isCurrentUser: participant.user._id.toString() === req.user.id
      }));

    res.json({
      challenge: {
        ...challenge.toObject(),
        userParticipating: !!userParticipation,
        userProgress,
        userCompleted: userParticipation ? userParticipation.completed : false,
        daysRemaining: Math.max(0, Math.ceil((challenge.endDate - new Date()) / (1000 * 60 * 60 * 24))),
        leaderboard
      }
    });
  } catch (error) {
    console.error('Get challenge details error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/challenges/user/participating
// @desc    Get challenges user is participating in
// @access  Private
router.get('/user/participating', auth, async (req, res) => {
  try {
    const challenges = await Challenge.getUserChallenges(req.user.id);

    const challengesWithProgress = await Promise.all(challenges.map(async (challenge) => {
      const userParticipation = challenge.participants.find(
        p => p.user.toString() === req.user.id
      );

      // Calculate current progress
      const completedHabits = await HabitTracking.countDocuments({
        user: req.user.id,
        habit: { $in: challenge.habits.map(h => h._id) },
        date: { $gte: challenge.startDate, $lte: new Date() },
        completed: true
      });

      const totalRequired = challenge.habits.length * challenge.duration;
      const progress = totalRequired > 0 ? Math.round((completedHabits / totalRequired) * 100) : 0;

      return {
        ...challenge.toObject(),
        userProgress: progress,
        userCompleted: userParticipation ? userParticipation.completed : false,
        daysRemaining: Math.max(0, Math.ceil((challenge.endDate - new Date()) / (1000 * 60 * 60 * 24)))
      };
    }));

    res.json({
      challenges: challengesWithProgress,
      total: challengesWithProgress.length
    });
  } catch (error) {
    console.error('Get user challenges error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
