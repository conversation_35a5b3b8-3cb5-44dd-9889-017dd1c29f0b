const express = require('express');
const { auth } = require('../middleware/auth');
const User = require('../models/User');
const HabitTracking = require('../models/HabitTracking');

const router = express.Router();

// @route   GET /api/community/leaderboard
// @desc    Get community leaderboard
// @access  Private
router.get('/leaderboard', auth, async (req, res) => {
  try {
    const { type = 'points', period = 'all', limit = 50 } = req.query;

    let matchCondition = {};

    // Set time period for leaderboard
    if (period !== 'all') {
      const now = new Date();
      let startDate;

      switch (period) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }
      matchCondition.createdAt = { $gte: startDate };
    }

    let leaderboard;

    if (type === 'points') {
      // Points leaderboard
      leaderboard = await User.find({
        isActive: true,
        'preferences.privacy.leaderboardVisible': true,
        ...matchCondition
      })
      .select('name avatar ecoProfile.totalPoints ecoProfile.level ecoProfile.streak location.city')
      .sort({ 'ecoProfile.totalPoints': -1 })
      .limit(parseInt(limit));

    } else if (type === 'impact') {
      // Environmental impact leaderboard
      leaderboard = await User.find({
        isActive: true,
        'preferences.privacy.leaderboardVisible': true,
        ...matchCondition
      })
      .select('name avatar ecoProfile.impact ecoProfile.level location.city')
      .sort({ 'ecoProfile.impact.co2Saved': -1 })
      .limit(parseInt(limit));

    } else if (type === 'streak') {
      // Streak leaderboard
      leaderboard = await User.find({
        isActive: true,
        'preferences.privacy.leaderboardVisible': true,
        ...matchCondition
      })
      .select('name avatar ecoProfile.streak ecoProfile.longestStreak ecoProfile.level location.city')
      .sort({ 'ecoProfile.streak': -1, 'ecoProfile.longestStreak': -1 })
      .limit(parseInt(limit));
    }

    // Add rank to each user
    const rankedLeaderboard = leaderboard.map((user, index) => ({
      ...user.toObject(),
      rank: index + 1,
      isCurrentUser: user._id.toString() === req.user.id
    }));

    // Find current user's position if not in top results
    let currentUserRank = null;
    const currentUserInTop = rankedLeaderboard.find(user => user.isCurrentUser);

    if (!currentUserInTop) {
      // Calculate user's actual rank
      let countQuery = {
        isActive: true,
        'preferences.privacy.leaderboardVisible': true,
        ...matchCondition
      };

      if (type === 'points') {
        countQuery['ecoProfile.totalPoints'] = { $gt: req.user.ecoProfile.totalPoints };
      } else if (type === 'impact') {
        countQuery['ecoProfile.impact.co2Saved'] = { $gt: req.user.ecoProfile.impact.co2Saved };
      } else if (type === 'streak') {
        countQuery['ecoProfile.streak'] = { $gt: req.user.ecoProfile.streak };
      }

      const usersAhead = await User.countDocuments(countQuery);
      currentUserRank = usersAhead + 1;
    }

    res.json({
      leaderboard: rankedLeaderboard,
      currentUserRank: currentUserInTop ? currentUserInTop.rank : currentUserRank,
      type,
      period,
      total: leaderboard.length
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/community/stats
// @desc    Get community statistics
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    const totalUsers = await User.countDocuments({ isActive: true });
    const totalHabitsCompleted = await HabitTracking.countDocuments({ completed: true });

    // Calculate total environmental impact
    const impactStats = await User.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          totalCO2Saved: { $sum: '$ecoProfile.impact.co2Saved' },
          totalWaterSaved: { $sum: '$ecoProfile.impact.waterSaved' },
          totalElectricitySaved: { $sum: '$ecoProfile.impact.electricitySaved' },
          totalPlasticAvoided: { $sum: '$ecoProfile.impact.plasticAvoided' },
          totalTreesEquivalent: { $sum: '$ecoProfile.impact.treesEquivalent' }
        }
      }
    ]);

    const impact = impactStats[0] || {
      totalCO2Saved: 0,
      totalWaterSaved: 0,
      totalElectricitySaved: 0,
      totalPlasticAvoided: 0,
      totalTreesEquivalent: 0
    };

    // Get active users today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const activeToday = await HabitTracking.distinct('user', {
      date: { $gte: today },
      completed: true
    });

    res.json({
      community: {
        totalUsers,
        activeToday: activeToday.length,
        totalHabitsCompleted,
        impact: {
          co2Saved: Math.round(impact.totalCO2Saved * 100) / 100,
          waterSaved: Math.round(impact.totalWaterSaved),
          electricitySaved: Math.round(impact.totalElectricitySaved * 100) / 100,
          plasticAvoided: Math.round(impact.totalPlasticAvoided),
          treesEquivalent: Math.round(impact.totalTreesEquivalent * 100) / 100
        }
      }
    });
  } catch (error) {
    console.error('Get community stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/community/follow/:userId
// @desc    Follow/unfollow a user
// @access  Private
router.post('/follow/:userId', auth, async (req, res) => {
  try {
    const { userId } = req.params;

    if (userId === req.user.id) {
      return res.status(400).json({ message: 'Cannot follow yourself' });
    }

    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    const currentUser = await User.findById(req.user.id);

    // Check if already following
    const isFollowing = currentUser.social.following.includes(userId);

    if (isFollowing) {
      // Unfollow
      currentUser.social.following.pull(userId);
      targetUser.social.followers.pull(req.user.id);
      await currentUser.save();
      await targetUser.save();

      res.json({
        message: 'Unfollowed successfully',
        isFollowing: false,
        followersCount: targetUser.social.followers.length
      });
    } else {
      // Follow
      currentUser.social.following.push(userId);
      targetUser.social.followers.push(req.user.id);
      await currentUser.save();
      await targetUser.save();

      res.json({
        message: 'Following successfully',
        isFollowing: true,
        followersCount: targetUser.social.followers.length
      });
    }
  } catch (error) {
    console.error('Follow/unfollow error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/community/following
// @desc    Get users that current user is following
// @access  Private
router.get('/following', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('social.following', 'name avatar ecoProfile.totalPoints ecoProfile.level ecoProfile.streak location.city')
      .select('social.following');

    res.json({
      following: user.social.following,
      count: user.social.following.length
    });
  } catch (error) {
    console.error('Get following error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/community/followers
// @desc    Get users following current user
// @access  Private
router.get('/followers', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('social.followers', 'name avatar ecoProfile.totalPoints ecoProfile.level ecoProfile.streak location.city')
      .select('social.followers');

    res.json({
      followers: user.social.followers,
      count: user.social.followers.length
    });
  } catch (error) {
    console.error('Get followers error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/community/feed
// @desc    Get activity feed from followed users
// @access  Private
router.get('/feed', auth, async (req, res) => {
  try {
    const { limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    const user = await User.findById(req.user.id).select('social.following');
    const followingIds = user.social.following;

    // Get recent activities from followed users
    const activities = await HabitTracking.find({
      user: { $in: followingIds },
      completed: true
    })
    .populate('user', 'name avatar ecoProfile.level')
    .populate('habit', 'name icon category points')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip(skip);

    // Format activities for feed
    const feed = activities.map(activity => ({
      id: activity._id,
      type: 'habit_completed',
      user: activity.user,
      habit: activity.habit,
      pointsEarned: activity.pointsEarned,
      streak: activity.streak,
      mood: activity.mood,
      createdAt: activity.createdAt,
      message: `${activity.user.name} completed "${activity.habit.name}" and earned ${activity.pointsEarned} points!`
    }));

    res.json({
      feed,
      page: parseInt(page),
      hasMore: activities.length === parseInt(limit)
    });
  } catch (error) {
    console.error('Get feed error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
