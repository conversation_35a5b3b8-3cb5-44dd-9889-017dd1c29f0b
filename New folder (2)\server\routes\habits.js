const express = require('express');
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const Habit = require('../models/Habit');
const HabitTracking = require('../models/HabitTracking');

const router = express.Router();

// @route   GET /api/habits/user
// @desc    Get user's habits with tracking status
// @access  Private
router.get('/user', auth, async (req, res) => {
  try {
    const { language = 'en' } = req.query;

    // Get default habits and user's custom habits
    const defaultHabits = await Habit.getDefaultHabits();
    const customHabits = await Habit.find({
      createdBy: req.user.id,
      isActive: true
    });

    const allHabits = [...defaultHabits, ...customHabits];

    // Get today's tracking data
    const today = new Date().toISOString().split('T')[0];
    const todayTracking = await HabitTracking.getUserTrackingForDate(req.user.id, today);

    // Add completion status and streak info to habits
    const habitsWithStatus = await Promise.all(allHabits.map(async (habit) => {
      const tracking = todayTracking.find(t => t.habit._id.toString() === habit._id.toString());
      const streak = await HabitTracking.getUserHabitStreak(req.user.id, habit._id);

      return {
        ...habit.toLocalizedJSON(language),
        completedToday: !!tracking,
        streak,
        lastCompleted: tracking ? tracking.date : null,
        totalCompletions: await HabitTracking.countDocuments({
          user: req.user.id,
          habit: habit._id,
          completed: true
        })
      };
    }));

    res.json({
      habits: habitsWithStatus,
      todayProgress: {
        completed: todayTracking.length,
        total: allHabits.length,
        percentage: allHabits.length > 0 ? Math.round((todayTracking.length / allHabits.length) * 100) : 0
      }
    });
  } catch (error) {
    console.error('Get habits error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/habits
// @desc    Create new custom habit
// @access  Private
router.post('/', [
  auth,
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2-100 characters'),
  body('category').isIn(['transportation', 'energy', 'water', 'waste', 'food', 'shopping', 'lifestyle', 'nature', 'community']),
  body('points').isInt({ min: 1, max: 100 }).withMessage('Points must be between 1-100'),
  body('difficulty').isIn(['easy', 'medium', 'hard'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const habitData = {
      ...req.body,
      createdBy: req.user.id,
      isDefault: false
    };

    const habit = new Habit(habitData);
    await habit.save();

    res.status(201).json({
      message: 'Custom habit created successfully',
      habit: habit.toLocalizedJSON(req.query.language || 'en')
    });
  } catch (error) {
    console.error('Create habit error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/habits/:id
// @desc    Update user's custom habit
// @access  Private
router.put('/:id', [
  auth,
  body('name').optional().trim().isLength({ min: 2, max: 100 }),
  body('points').optional().isInt({ min: 1, max: 100 }),
  body('difficulty').optional().isIn(['easy', 'medium', 'hard'])
], async (req, res) => {
  try {
    const habit = await Habit.findOne({
      _id: req.params.id,
      createdBy: req.user.id,
      isDefault: false
    });

    if (!habit) {
      return res.status(404).json({ message: 'Habit not found or not authorized' });
    }

    Object.assign(habit, req.body);
    await habit.save();

    res.json({
      message: 'Habit updated successfully',
      habit: habit.toLocalizedJSON(req.query.language || 'en')
    });
  } catch (error) {
    console.error('Update habit error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/habits/:id
// @desc    Delete user's custom habit
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const habit = await Habit.findOne({
      _id: req.params.id,
      createdBy: req.user.id,
      isDefault: false
    });

    if (!habit) {
      return res.status(404).json({ message: 'Habit not found or not authorized' });
    }

    // Also delete all tracking records for this habit
    await HabitTracking.deleteMany({ habit: req.params.id, user: req.user.id });
    await habit.deleteOne();

    res.json({ message: 'Habit deleted successfully' });
  } catch (error) {
    console.error('Delete habit error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/habits/categories
// @desc    Get habit categories with counts
// @access  Private
router.get('/categories', auth, async (req, res) => {
  try {
    const categories = await Habit.aggregate([
      {
        $match: {
          $or: [
            { isDefault: true },
            { createdBy: req.user._id }
          ],
          isActive: true
        }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalPoints: { $sum: '$points' },
          avgDifficulty: { $avg: { $cond: [
            { $eq: ['$difficulty', 'easy'] }, 1,
            { $cond: [{ $eq: ['$difficulty', 'medium'] }, 2, 3] }
          ]}}
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    res.json({ categories });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/habits/popular
// @desc    Get popular habits based on user completions
// @access  Private
router.get('/popular', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const popularHabits = await Habit.aggregate([
      {
        $match: { isActive: true }
      },
      {
        $lookup: {
          from: 'habittrackings',
          localField: '_id',
          foreignField: 'habit',
          as: 'completions'
        }
      },
      {
        $addFields: {
          completionCount: { $size: '$completions' },
          uniqueUsers: { $size: { $setUnion: ['$completions.user', []] } }
        }
      },
      {
        $sort: { completionCount: -1, uniqueUsers: -1 }
      },
      {
        $limit: parseInt(limit)
      }
    ]);

    res.json({ habits: popularHabits });
  } catch (error) {
    console.error('Get popular habits error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
