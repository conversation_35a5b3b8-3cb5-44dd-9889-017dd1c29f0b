const express = require('express');
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const EcoPlace = require('../models/EcoPlace');

const router = express.Router();

// @route   GET /api/map/places
// @desc    Get eco-friendly places near user
// @access  Private
router.get('/places', auth, async (req, res) => {
  try {
    const {
      lat,
      lng,
      radius = 10000, // meters
      type,
      limit = 50
    } = req.query;

    let places;

    if (lat && lng) {
      // Find places near coordinates
      places = await EcoPlace.findNearby(
        parseFloat(lng),
        parseFloat(lat),
        parseInt(radius),
        type
      );
    } else if (type && type !== 'all') {
      // Get places by type
      places = await EcoPlace.getByType(type);
    } else {
      // Get all approved places
      places = await EcoPlace.find({
        status: 'approved',
        isActive: true
      })
      .populate('submittedBy', 'name')
      .limit(parseInt(limit))
      .sort({ 'rating.average': -1, visitCount: -1 });
    }

    // Add user-specific data
    const placesWithUserData = places.map(place => {
      const isFavorite = place.favorites.includes(req.user.id);
      const userReview = place.reviews.find(r => r.user.toString() === req.user.id);

      return {
        ...place.toObject(),
        isFavorite,
        userReview: userReview || null,
        distance: place.distance || null
      };
    });

    res.json({
      places: placesWithUserData,
      count: placesWithUserData.length,
      filters: { type, radius: parseInt(radius), limit: parseInt(limit) }
    });

  } catch (error) {
    console.error('Get places error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/map/places
// @desc    Submit a new eco-friendly place
// @access  Private
router.post('/places', [
  auth,
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('type').isIn([
    'recycling_center', 'organic_shop', 'ev_charging', 'renewable_energy',
    'sustainable_business', 'community_garden', 'farmers_market', 'repair_cafe',
    'zero_waste_store', 'composting_facility'
  ]).withMessage('Invalid place type'),
  body('coordinates').isArray({ min: 2, max: 2 }).withMessage('Coordinates must be [longitude, latitude]'),
  body('address.formatted').trim().isLength({ min: 5 }).withMessage('Address is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const placeData = {
      ...req.body,
      location: {
        type: 'Point',
        coordinates: req.body.coordinates
      },
      submittedBy: req.user.id,
      status: 'pending' // Requires approval
    };

    const place = new EcoPlace(placeData);
    await place.save();

    res.status(201).json({
      message: 'Place submitted successfully! It will be reviewed by our team.',
      place: {
        id: place._id,
        name: place.name,
        type: place.type,
        status: place.status
      }
    });
  } catch (error) {
    console.error('Submit place error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/map/places/:id
// @desc    Get specific place details
// @access  Private
router.get('/places/:id', auth, async (req, res) => {
  try {
    const place = await EcoPlace.findOne({
      _id: req.params.id,
      status: 'approved',
      isActive: true
    })
    .populate('submittedBy', 'name avatar')
    .populate('reviews.user', 'name avatar');

    if (!place) {
      return res.status(404).json({ message: 'Place not found' });
    }

    // Increment visit count
    place.visitCount += 1;
    await place.save();

    // Check if user has favorited this place
    const isFavorite = place.favorites.includes(req.user.id);
    const userReview = place.reviews.find(r => r.user._id.toString() === req.user.id);

    res.json({
      place: {
        ...place.toObject(),
        isFavorite,
        userReview: userReview || null
      }
    });

  } catch (error) {
    console.error('Get place error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/map/places/:id/favorite
// @desc    Toggle favorite status for a place
// @access  Private
router.post('/places/:id/favorite', auth, async (req, res) => {
  try {
    const place = await EcoPlace.findOne({
      _id: req.params.id,
      status: 'approved',
      isActive: true
    });

    if (!place) {
      return res.status(404).json({ message: 'Place not found' });
    }

    const isFavorite = place.toggleFavorite(req.user.id);
    await place.save();

    res.json({
      message: isFavorite ? 'Added to favorites' : 'Removed from favorites',
      isFavorite,
      favoritesCount: place.favorites.length
    });
  } catch (error) {
    console.error('Toggle favorite error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/map/places/:id/review
// @desc    Add review for a place
// @access  Private
router.post('/places/:id/review', [
  auth,
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be 1-5'),
  body('comment').optional().trim().isLength({ max: 300 }).withMessage('Comment cannot exceed 300 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { rating, comment } = req.body;

    const place = await EcoPlace.findOne({
      _id: req.params.id,
      status: 'approved',
      isActive: true
    });

    if (!place) {
      return res.status(404).json({ message: 'Place not found' });
    }

    const added = place.addReview(req.user.id, rating, comment);
    if (!added) {
      return res.status(400).json({ message: 'You have already reviewed this place' });
    }

    await place.save();

    res.json({
      message: 'Review added successfully',
      rating: {
        average: place.rating.average,
        count: place.rating.count
      }
    });
  } catch (error) {
    console.error('Add review error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/map/favorites
// @desc    Get user's favorite places
// @access  Private
router.get('/favorites', auth, async (req, res) => {
  try {
    const favorites = await EcoPlace.getUserFavorites(req.user.id);

    res.json({
      favorites,
      count: favorites.length
    });
  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/map/types
// @desc    Get available place types with counts
// @access  Private
router.get('/types', auth, async (req, res) => {
  try {
    const typeCounts = await EcoPlace.aggregate([
      {
        $match: { status: 'approved', isActive: true }
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          avgRating: { $avg: '$rating.average' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    const types = [
      {
        value: 'all',
        label: 'All Places',
        icon: '🌍',
        description: 'Show all eco-friendly places',
        count: typeCounts.reduce((sum, type) => sum + type.count, 0)
      },
      {
        value: 'recycling_center',
        label: 'Recycling Centers',
        icon: '♻️',
        description: 'Waste recycling and disposal centers',
        count: typeCounts.find(t => t._id === 'recycling_center')?.count || 0
      },
      {
        value: 'organic_shop',
        label: 'Organic Shops',
        icon: '🥬',
        description: 'Organic food and eco-friendly product stores',
        count: typeCounts.find(t => t._id === 'organic_shop')?.count || 0
      },
      {
        value: 'ev_charging',
        label: 'EV Charging Stations',
        icon: '🔌',
        description: 'Electric vehicle charging points',
        count: typeCounts.find(t => t._id === 'ev_charging')?.count || 0
      },
      {
        value: 'renewable_energy',
        label: 'Renewable Energy',
        icon: '☀️',
        description: 'Solar, wind, and other renewable energy services',
        count: typeCounts.find(t => t._id === 'renewable_energy')?.count || 0
      },
      {
        value: 'sustainable_business',
        label: 'Sustainable Businesses',
        icon: '🏪',
        description: 'Eco-friendly restaurants, cafes, and shops',
        count: typeCounts.find(t => t._id === 'sustainable_business')?.count || 0
      },
      {
        value: 'community_garden',
        label: 'Community Gardens',
        icon: '🌱',
        description: 'Public gardens and green spaces',
        count: typeCounts.find(t => t._id === 'community_garden')?.count || 0
      },
      {
        value: 'farmers_market',
        label: 'Farmers Markets',
        icon: '🥕',
        description: 'Local farmers markets and organic produce',
        count: typeCounts.find(t => t._id === 'farmers_market')?.count || 0
      },
      {
        value: 'repair_cafe',
        label: 'Repair Cafes',
        icon: '🔧',
        description: 'Places to repair and upcycle items',
        count: typeCounts.find(t => t._id === 'repair_cafe')?.count || 0
      },
      {
        value: 'zero_waste_store',
        label: 'Zero Waste Stores',
        icon: '🌿',
        description: 'Package-free and zero waste shops',
        count: typeCounts.find(t => t._id === 'zero_waste_store')?.count || 0
      }
    ];

    res.json({ types });

  } catch (error) {
    console.error('Get types error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/map/search
// @desc    Search places by name or description
// @access  Private
router.get('/search', auth, async (req, res) => {
  try {
    const { q, type, limit = 20 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({ message: 'Search query must be at least 2 characters' });
    }

    let query = {
      status: 'approved',
      isActive: true,
      $or: [
        { name: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { tags: { $in: [new RegExp(q, 'i')] } }
      ]
    };

    if (type && type !== 'all') {
      query.type = type;
    }

    const places = await EcoPlace.find(query)
      .populate('submittedBy', 'name')
      .limit(parseInt(limit))
      .sort({ 'rating.average': -1, visitCount: -1 });

    // Add user-specific data
    const placesWithUserData = places.map(place => {
      const isFavorite = place.favorites.includes(req.user.id);
      return {
        ...place.toObject(),
        isFavorite
      };
    });

    res.json({
      places: placesWithUserData,
      count: placesWithUserData.length,
      query: q
    });
  } catch (error) {
    console.error('Search places error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
