const express = require('express');
const { auth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/points
// @desc    Get user points and level
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    res.json({ 
      message: 'Points endpoint - coming soon',
      points: req.user.ecoProfile.totalPoints,
      level: req.user.ecoProfile.level
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
