const express = require('express');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Sample eco tips
const ecoTips = [
  {
    id: 1,
    title: "Switch to LED Bulbs",
    content: "LED bulbs use 75% less energy and last 25 times longer than incandescent bulbs.",
    category: "energy",
    impact: { co2Saved: 0.5, electricitySaved: 0.8 },
    translations: {
      ta: {
        title: "LED விளக்குகளுக்கு மாறுங்கள்",
        content: "LED விளக்குகள் 75% குறைவான ஆற்றலைப் பயன்படுத்துகின்றன மற்றும் வெள்ளை விளக்குகளை விட 25 மடங்கு நீண்ட காலம் நீடிக்கும்."
      },
      tanglish: {
        title: "LED bulbs-ku change pannunga",
        content: "LED bulbs 75% less energy use pannudhu and normal bulbs-a vida 25 times long last aagudhu."
      }
    }
  },
  {
    id: 2,
    title: "Use Public Transport",
    content: "Taking public transport instead of driving can reduce your carbon footprint by up to 45%.",
    category: "transportation",
    impact: { co2Saved: 2.3 },
    translations: {
      ta: {
        title: "பொது போக்குவரத்தைப் பயன்படுத்துங்கள்",
        content: "வாகனம் ஓட்டுவதற்கு பதிலாக பொது போக்குவரத்தைப் பயன்படுத்துவது உங்கள் கார்பன் தடத்தை 45% வரை குறைக்கலாம்."
      },
      tanglish: {
        title: "Public transport use pannunga",
        content: "Car drive panna vida public transport use pannina unga carbon footprint 45% varaikum reduce aagum."
      }
    }
  },
  {
    id: 3,
    title: "Carry Reusable Bags",
    content: "A single reusable bag can eliminate the need for 1,000 plastic bags over its lifetime.",
    category: "waste",
    impact: { plasticAvoided: 50 },
    translations: {
      ta: {
        title: "மீண்டும் பயன்படுத்தக்கூடிய பைகளை எடுத்துச் செல்லுங்கள்",
        content: "ஒரு மீண்டும் பயன்படுத்தக்கூடிய பை அதன் வாழ்நாளில் 1,000 பிளாஸ்டிக் பைகளின் தேவையை நீக்கலாம்."
      },
      tanglish: {
        title: "Reusable bags carry pannunga",
        content: "Oru reusable bag lifetime-la 1,000 plastic bags-oda need-a eliminate panna mudiyum."
      }
    }
  },
  {
    id: 4,
    title: "Fix Water Leaks",
    content: "A single dripping faucet can waste over 3,000 gallons of water per year.",
    category: "water",
    impact: { waterSaved: 10 },
    translations: {
      ta: {
        title: "நீர் கசிவுகளை சரிசெய்யுங்கள்",
        content: "ஒரு சொட்டு சொட்டாக கசியும் குழாய் ஆண்டுக்கு 3,000 கேலன்களுக்கு மேல் தண்ணீரை வீணாக்கலாம்."
      },
      tanglish: {
        title: "Water leaks fix pannunga",
        content: "Oru dripping tap year-ku 3,000 gallons water waste panna mudiyum."
      }
    }
  },
  {
    id: 5,
    title: "Eat More Plant-Based Meals",
    content: "Plant-based meals typically require 75% less energy and produce 75% fewer emissions than meat-based meals.",
    category: "food",
    impact: { co2Saved: 1.8, waterSaved: 15 },
    translations: {
      ta: {
        title: "அதிக தாவர அடிப்படையிலான உணவுகளை சாப்பிடுங்கள்",
        content: "தாவர அடிப்படையிலான உணவுகள் பொதுவாக 75% குறைவான ஆற்றல் தேவைப்படுகிறது மற்றும் இறைச்சி அடிப்படையிலான உணவுகளை விட 75% குறைவான உமிழ்வுகளை உருவாக்குகிறது."
      },
      tanglish: {
        title: "Plant-based meals adhigama saapdunga",
        content: "Plant-based meals typically 75% less energy require pannudhu and meat meals-a vida 75% less emissions produce pannudhu."
      }
    }
  }
];

// @route   GET /api/tips/daily
// @desc    Get daily eco tip
// @access  Private
router.get('/daily', auth, async (req, res) => {
  try {
    const { language = 'en' } = req.query;
    
    // Get a random tip for today (you could make this more sophisticated)
    const today = new Date();
    const tipIndex = today.getDate() % ecoTips.length;
    const tip = ecoTips[tipIndex];
    
    // Localize the tip
    let localizedTip = { ...tip };
    if (language !== 'en' && tip.translations[language]) {
      localizedTip.title = tip.translations[language].title;
      localizedTip.content = tip.translations[language].content;
    }
    
    res.json({ 
      tip: localizedTip,
      message: 'Daily eco tip retrieved successfully'
    });
  } catch (error) {
    console.error('Get daily tip error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tips/category/:category
// @desc    Get tips by category
// @access  Private
router.get('/category/:category', auth, async (req, res) => {
  try {
    const { category } = req.params;
    const { language = 'en' } = req.query;
    
    const categoryTips = ecoTips.filter(tip => tip.category === category);
    
    // Localize tips
    const localizedTips = categoryTips.map(tip => {
      let localizedTip = { ...tip };
      if (language !== 'en' && tip.translations[language]) {
        localizedTip.title = tip.translations[language].title;
        localizedTip.content = tip.translations[language].content;
      }
      return localizedTip;
    });
    
    res.json({ 
      tips: localizedTips,
      category,
      count: localizedTips.length
    });
  } catch (error) {
    console.error('Get category tips error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tips/random
// @desc    Get random eco tips
// @access  Private
router.get('/random', auth, async (req, res) => {
  try {
    const { count = 3, language = 'en' } = req.query;
    
    // Get random tips
    const shuffled = [...ecoTips].sort(() => 0.5 - Math.random());
    const randomTips = shuffled.slice(0, parseInt(count));
    
    // Localize tips
    const localizedTips = randomTips.map(tip => {
      let localizedTip = { ...tip };
      if (language !== 'en' && tip.translations[language]) {
        localizedTip.title = tip.translations[language].title;
        localizedTip.content = tip.translations[language].content;
      }
      return localizedTip;
    });
    
    res.json({ 
      tips: localizedTips,
      count: localizedTips.length
    });
  } catch (error) {
    console.error('Get random tips error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tips/categories
// @desc    Get all tip categories
// @access  Private
router.get('/categories', auth, async (req, res) => {
  try {
    const categories = [...new Set(ecoTips.map(tip => tip.category))];
    
    res.json({ 
      categories,
      count: categories.length
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
