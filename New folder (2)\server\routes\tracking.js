const express = require('express');
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const HabitTracking = require('../models/HabitTracking');
const Habit = require('../models/Habit');
const User = require('../models/User');

const router = express.Router();

// @route   GET /api/tracking/date/:date
// @desc    Get tracking for specific date
// @access  Private
router.get('/date/:date', auth, async (req, res) => {
  try {
    const tracking = await HabitTracking.getUserTrackingForDate(
      req.user.id,
      req.params.date
    );

    res.json({ tracking });
  } catch (error) {
    console.error('Get tracking error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/tracking/complete
// @desc    Complete a habit
// @access  Private
router.post('/complete', [
  auth,
  body('habitId').isMongoId().withMessage('Valid habit ID required'),
  body('date').isISO8601().withMessage('Valid date required'),
  body('notes').optional().isLength({ max: 200 }).withMessage('Notes cannot exceed 200 characters'),
  body('mood').optional().isIn(['excited', 'happy', 'neutral', 'motivated', 'proud']),
  body('difficulty').optional().isInt({ min: 1, max: 5 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { habitId, date, notes, mood, difficulty } = req.body;

    // Check if habit exists and user has access
    const habit = await Habit.findOne({
      _id: habitId,
      $or: [
        { isDefault: true },
        { createdBy: req.user.id }
      ],
      isActive: true
    });

    if (!habit) {
      return res.status(404).json({ message: 'Habit not found' });
    }

    // Check if already completed today
    const existingTracking = await HabitTracking.findOne({
      user: req.user.id,
      habit: habitId,
      date: {
        $gte: new Date(date + 'T00:00:00.000Z'),
        $lt: new Date(date + 'T23:59:59.999Z')
      }
    });

    if (existingTracking) {
      return res.status(400).json({ message: 'Habit already completed today' });
    }

    // Calculate streak
    const currentStreak = await HabitTracking.getUserHabitStreak(req.user.id, habitId);
    const newStreak = currentStreak + 1;

    // Calculate bonus points for streak
    let bonusPoints = 0;
    if (newStreak >= 7) bonusPoints += 5;   // Weekly streak bonus
    if (newStreak >= 30) bonusPoints += 15; // Monthly streak bonus
    if (newStreak >= 100) bonusPoints += 30; // 100-day streak bonus

    // Create tracking entry
    const tracking = new HabitTracking({
      user: req.user.id,
      habit: habitId,
      date: new Date(date),
      completed: true,
      pointsEarned: habit.points + bonusPoints,
      impact: habit.impact,
      notes: notes || '',
      mood: mood || 'happy',
      difficulty: difficulty || 3,
      streak: newStreak
    });

    await tracking.save();
    await tracking.populate('habit');

    // Update user stats
    const user = await User.findById(req.user.id);
    user.addPoints(tracking.pointsEarned);
    user.updateImpact(habit.impact);
    user.ecoProfile.streak = Math.max(user.ecoProfile.streak, newStreak);
    user.ecoProfile.longestStreak = Math.max(user.ecoProfile.longestStreak, newStreak);

    // Check for new badges
    const badges = checkForNewBadges(user, newStreak, tracking.pointsEarned);
    badges.forEach(badge => user.addBadge(badge));

    await user.save();

    // Update habit statistics
    habit.incrementStats();
    await habit.save();

    res.json({
      message: 'Habit completed successfully!',
      tracking,
      pointsEarned: tracking.pointsEarned,
      bonusPoints,
      newStreak,
      newBadges: badges,
      userStats: {
        totalPoints: user.ecoProfile.totalPoints,
        level: user.ecoProfile.level,
        streak: user.ecoProfile.streak
      }
    });
  } catch (error) {
    console.error('Complete habit error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/tracking/:id
// @desc    Undo habit completion
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const tracking = await HabitTracking.findOne({
      _id: req.params.id,
      user: req.user.id
    }).populate('habit');

    if (!tracking) {
      return res.status(404).json({ message: 'Tracking record not found' });
    }

    // Check if it's from today (only allow undo for today)
    const today = new Date().toISOString().split('T')[0];
    const trackingDate = tracking.date.toISOString().split('T')[0];

    if (trackingDate !== today) {
      return res.status(400).json({ message: 'Can only undo habits completed today' });
    }

    // Update user stats (subtract points and impact)
    const user = await User.findById(req.user.id);
    user.ecoProfile.totalPoints = Math.max(0, user.ecoProfile.totalPoints - tracking.pointsEarned);
    user.calculateLevel();

    // Subtract impact
    const impact = user.ecoProfile.impact;
    impact.co2Saved = Math.max(0, impact.co2Saved - tracking.impact.co2Saved);
    impact.waterSaved = Math.max(0, impact.waterSaved - tracking.impact.waterSaved);
    impact.electricitySaved = Math.max(0, impact.electricitySaved - tracking.impact.electricitySaved);
    impact.plasticAvoided = Math.max(0, impact.plasticAvoided - tracking.impact.plasticAvoided);

    await user.save();
    await tracking.deleteOne();

    res.json({
      message: 'Habit completion undone',
      userStats: {
        totalPoints: user.ecoProfile.totalPoints,
        level: user.ecoProfile.level
      }
    });
  } catch (error) {
    console.error('Undo tracking error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tracking/streaks
// @desc    Get user's habit streaks
// @access  Private
router.get('/streaks', auth, async (req, res) => {
  try {
    // Get all user's habits
    const defaultHabits = await Habit.getDefaultHabits();
    const customHabits = await Habit.find({
      createdBy: req.user.id,
      isActive: true
    });
    const allHabits = [...defaultHabits, ...customHabits];

    // Calculate streaks for each habit
    const streaks = {};
    for (const habit of allHabits) {
      streaks[habit._id] = await HabitTracking.getUserHabitStreak(req.user.id, habit._id);
    }

    res.json({ streaks });
  } catch (error) {
    console.error('Get streaks error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tracking/calendar/:year/:month
// @desc    Get habit tracking calendar for a month
// @access  Private
router.get('/calendar/:year/:month', auth, async (req, res) => {
  try {
    const { year, month } = req.params;
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59, 999);

    const tracking = await HabitTracking.find({
      user: req.user.id,
      date: { $gte: startDate, $lte: endDate },
      completed: true
    }).populate('habit', 'name icon category points');

    // Group by date
    const calendar = {};
    tracking.forEach(track => {
      const dateKey = track.date.toISOString().split('T')[0];
      if (!calendar[dateKey]) {
        calendar[dateKey] = [];
      }
      calendar[dateKey].push(track);
    });

    res.json({ calendar, month: parseInt(month), year: parseInt(year) });
  } catch (error) {
    console.error('Get calendar error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to check for new badges
function checkForNewBadges(user, streak, pointsEarned) {
  const badges = [];

  // Streak badges
  if (streak === 7) {
    badges.push({
      name: 'Week Warrior',
      description: 'Completed habits for 7 days straight!',
      icon: '🔥',
      earnedAt: new Date()
    });
  } else if (streak === 30) {
    badges.push({
      name: 'Month Master',
      description: 'Completed habits for 30 days straight!',
      icon: '🏆',
      earnedAt: new Date()
    });
  } else if (streak === 100) {
    badges.push({
      name: 'Century Champion',
      description: 'Completed habits for 100 days straight!',
      icon: '👑',
      earnedAt: new Date()
    });
  }

  // Points badges
  if (user.ecoProfile.totalPoints >= 1000 && user.ecoProfile.totalPoints - pointsEarned < 1000) {
    badges.push({
      name: 'Eco Enthusiast',
      description: 'Earned 1,000 eco points!',
      icon: '🌱',
      earnedAt: new Date()
    });
  } else if (user.ecoProfile.totalPoints >= 5000 && user.ecoProfile.totalPoints - pointsEarned < 5000) {
    badges.push({
      name: 'Green Guardian',
      description: 'Earned 5,000 eco points!',
      icon: '🌿',
      earnedAt: new Date()
    });
  }

  return badges;
}

module.exports = router;
