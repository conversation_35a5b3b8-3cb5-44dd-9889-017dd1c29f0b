const mongoose = require('mongoose');
const Habit = require('../models/Habit');
require('dotenv').config();

// Default habits data
const defaultHabits = [
  {
    name: "Use public transport",
    description: "Take bus, train, or metro instead of driving a personal vehicle",
    category: "transportation",
    icon: "🚌",
    color: "#3B82F6",
    points: 15,
    impact: {
      co2Saved: 2.3,
      waterSaved: 0,
      electricitySaved: 0,
      plasticAvoided: 0
    },
    difficulty: "easy",
    frequency: "daily",
    isDefault: true,
    tags: ["transport", "carbon", "public"],
    translations: {
      ta: {
        name: "பொது போக்குவரத்தைப் பயன்படுத்துங்கள்",
        description: "தனிப்பட்ட வாகனம் ஓட்டுவதற்கு பதிலாக பேருந்து, ரயில் அல்லது மெட்ரோவை எடுத்துக் கொள்ளுங்கள்"
      },
      tanglish: {
        name: "Public transport use pannunga",
        description: "Personal vehicle drive panna vida bus, train, or metro use pannunga"
      }
    }
  },
  {
    name: "Carry reusable bags",
    description: "Use cloth or reusable bags instead of plastic bags when shopping",
    category: "waste",
    icon: "🛍️",
    color: "#10B981",
    points: 10,
    impact: {
      co2Saved: 0.1,
      waterSaved: 0,
      electricitySaved: 0,
      plasticAvoided: 50
    },
    difficulty: "easy",
    frequency: "daily",
    isDefault: true,
    tags: ["plastic", "waste", "shopping"],
    translations: {
      ta: {
        name: "மீண்டும் பயன்படுத்தக்கூடிய பைகளை எடுத்துச் செல்லுங்கள்",
        description: "ஷாப்பிங் செய்யும் போது பிளாஸ்டிக் பைகளுக்கு பதிலாக துணி அல்லது மீண்டும் பயன்படுத்தக்கூடிய பைகளைப் பயன்படுத்துங்கள்"
      },
      tanglish: {
        name: "Reusable bags carry pannunga",
        description: "Shopping time-la plastic bags vida cloth or reusable bags use pannunga"
      }
    }
  },
  {
    name: "Switch off lights when leaving room",
    description: "Turn off lights and fans when you leave a room to save electricity",
    category: "energy",
    icon: "💡",
    color: "#F59E0B",
    points: 8,
    impact: {
      co2Saved: 0.5,
      waterSaved: 0,
      electricitySaved: 0.8,
      plasticAvoided: 0
    },
    difficulty: "easy",
    frequency: "daily",
    isDefault: true,
    tags: ["energy", "electricity", "lights"],
    translations: {
      ta: {
        name: "அறையை விட்டு வெளியேறும்போது விளக்குகளை அணைக்கவும்",
        description: "மின்சாரம் சேமிக்க அறையை விட்டு வெளியேறும்போது விளக்குகள் மற்றும் மின்விசிறிகளை அணைக்கவும்"
      },
      tanglish: {
        name: "Room vittu veliyerum bodhu lights off pannunga",
        description: "Electricity save panna room vittu pogum bodhu lights and fans off pannunga"
      }
    }
  },
  {
    name: "Use refillable water bottle",
    description: "Carry a refillable water bottle instead of buying plastic bottles",
    category: "waste",
    icon: "🚰",
    color: "#06B6D4",
    points: 12,
    impact: {
      co2Saved: 0.2,
      waterSaved: 1,
      electricitySaved: 0,
      plasticAvoided: 25
    },
    difficulty: "easy",
    frequency: "daily",
    isDefault: true,
    tags: ["plastic", "water", "bottle"],
    translations: {
      ta: {
        name: "மீண்டும் நிரப்பக்கூடிய தண்ணீர் பாட்டிலைப் பயன்படுத்துங்கள்",
        description: "பிளாஸ்டிக் பாட்டில்களை வாங்குவதற்கு பதிலாக மீண்டும் நிரப்பக்கூடிய தண்ணீர் பாட்டிலை எடுத்துச் செல்லுங்கள்"
      },
      tanglish: {
        name: "Refillable water bottle use pannunga",
        description: "Plastic bottles vaanga vida refillable water bottle carry pannunga"
      }
    }
  },
  {
    name: "Eat plant-based meal",
    description: "Choose a vegetarian or vegan meal to reduce environmental impact",
    category: "food",
    icon: "🥗",
    color: "#84CC16",
    points: 18,
    impact: {
      co2Saved: 1.8,
      waterSaved: 15,
      electricitySaved: 0,
      plasticAvoided: 0
    },
    difficulty: "medium",
    frequency: "daily",
    isDefault: true,
    tags: ["food", "vegetarian", "carbon"],
    translations: {
      ta: {
        name: "தாவர அடிப்படையிலான உணவை சாப்பிடுங்கள்",
        description: "சுற்றுச்சூழல் தாக்கத்தைக் குறைக்க சைவ அல்லது வீகன் உணவைத் தேர்ந்தெடுக்கவும்"
      },
      tanglish: {
        name: "Plant-based meal saapdunga",
        description: "Environmental impact reduce panna vegetarian or vegan meal choose pannunga"
      }
    }
  },
  {
    name: "Take shorter showers",
    description: "Limit shower time to 5 minutes or less to conserve water",
    category: "water",
    icon: "🚿",
    color: "#0EA5E9",
    points: 10,
    impact: {
      co2Saved: 0.3,
      waterSaved: 25,
      electricitySaved: 0.2,
      plasticAvoided: 0
    },
    difficulty: "medium",
    frequency: "daily",
    isDefault: true,
    tags: ["water", "shower", "conservation"],
    translations: {
      ta: {
        name: "குறுகிய குளியல் எடுங்கள்",
        description: "தண்ணீரைப் பாதுகாக்க குளியல் நேரத்தை 5 நிமிடங்கள் அல்லது அதற்கும் குறைவாக வரம்பிடுங்கள்"
      },
      tanglish: {
        name: "Shorter showers edunga",
        description: "Water conserve panna shower time-a 5 minutes or less-a limit pannunga"
      }
    }
  },
  {
    name: "Walk or bike for short trips",
    description: "Walk or cycle for trips under 2 miles instead of driving",
    category: "transportation",
    icon: "🚶‍♂️",
    color: "#8B5CF6",
    points: 20,
    impact: {
      co2Saved: 1.5,
      waterSaved: 0,
      electricitySaved: 0,
      plasticAvoided: 0
    },
    difficulty: "medium",
    frequency: "daily",
    isDefault: true,
    tags: ["transport", "walking", "cycling", "health"],
    translations: {
      ta: {
        name: "குறுகிய பயணங்களுக்கு நடக்கவும் அல்லது சைக்கிள் ஓட்டவும்",
        description: "வாகனம் ஓட்டுவதற்கு பதிலாக 2 மைல்களுக்கு குறைவான பயணங்களுக்கு நடக்கவும் அல்லது சைக்கிள் ஓட்டவும்"
      },
      tanglish: {
        name: "Short trips-ku walk or bike pannunga",
        description: "Drive panna vida 2 miles-ku kammiya irukura trips-ku walk or cycle pannunga"
      }
    }
  },
  {
    name: "Unplug electronics when not in use",
    description: "Unplug chargers and electronics to prevent phantom energy consumption",
    category: "energy",
    icon: "🔌",
    color: "#EF4444",
    points: 8,
    impact: {
      co2Saved: 0.4,
      waterSaved: 0,
      electricitySaved: 0.6,
      plasticAvoided: 0
    },
    difficulty: "easy",
    frequency: "daily",
    isDefault: true,
    tags: ["energy", "electronics", "phantom"],
    translations: {
      ta: {
        name: "பயன்படுத்தாதபோது மின்னணு சாதனங்களை துண்டிக்கவும்",
        description: "பாண்டம் ஆற்றல் நுகர்வைத் தடுக்க சார்ஜர்கள் மற்றும் மின்னணு சாதனங்களை துண்டிக்கவும்"
      },
      tanglish: {
        name: "Use pannama irukum bodhu electronics unplug pannunga",
        description: "Phantom energy consumption prevent panna chargers and electronics unplug pannunga"
      }
    }
  },
  {
    name: "Buy local and seasonal produce",
    description: "Choose locally grown, seasonal fruits and vegetables",
    category: "food",
    icon: "🥕",
    color: "#F97316",
    points: 15,
    impact: {
      co2Saved: 1.2,
      waterSaved: 5,
      electricitySaved: 0,
      plasticAvoided: 10
    },
    difficulty: "medium",
    frequency: "daily",
    isDefault: true,
    tags: ["food", "local", "seasonal"],
    translations: {
      ta: {
        name: "உள்ளூர் மற்றும் பருவகால பொருட்களை வாங்குங்கள்",
        description: "உள்ளூரில் வளர்க்கப்பட்ட, பருவகால பழங்கள் மற்றும் காய்கறிகளைத் தேர்ந்தெடுக்கவும்"
      },
      tanglish: {
        name: "Local and seasonal produce vaangunga",
        description: "Locally grown, seasonal fruits and vegetables choose pannunga"
      }
    }
  },
  {
    name: "Start composting",
    description: "Compost organic waste to reduce landfill waste and create fertilizer",
    category: "waste",
    icon: "🌱",
    color: "#22C55E",
    points: 25,
    impact: {
      co2Saved: 0.8,
      waterSaved: 3,
      electricitySaved: 0,
      plasticAvoided: 0
    },
    difficulty: "hard",
    frequency: "daily",
    isDefault: true,
    tags: ["waste", "compost", "organic"],
    translations: {
      ta: {
        name: "உரமாக்கல் தொடங்குங்கள்",
        description: "நிலப்பரப்பு கழிவுகளைக் குறைக்கவும் உரம் உருவாக்கவும் கரிம கழிவுகளை உரமாக்குங்கள்"
      },
      tanglish: {
        name: "Composting start pannunga",
        description: "Landfill waste reduce panna and fertilizer create panna organic waste compost pannunga"
      }
    }
  }
];

// Connect to MongoDB and seed data
async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/greenmate', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB');

    // Clear existing default habits
    await Habit.deleteMany({ isDefault: true });
    console.log('🗑️ Cleared existing default habits');

    // Insert new default habits
    await Habit.insertMany(defaultHabits);
    console.log(`🌱 Inserted ${defaultHabits.length} default habits`);

    console.log('✅ Database seeded successfully!');
    
    // List the habits that were created
    console.log('\n📋 Created habits:');
    defaultHabits.forEach((habit, index) => {
      console.log(`${index + 1}. ${habit.name} (${habit.category}) - ${habit.points} points`);
    });

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the seed function
if (require.main === module) {
  seedDatabase();
}

module.exports = { defaultHabits, seedDatabase };
