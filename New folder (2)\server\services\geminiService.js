const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    this.genAI = this.apiKey ? new GoogleGenerativeAI(this.apiKey) : null;
  }

  getModel() {
    if (!this.genAI) {
      throw new Error('Gemini API key not configured');
    }
    return this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  }

  async generateEcoAdvice(message, context = {}) {
    try {
      // If no API key, fall back to sample responses
      if (!this.apiKey) {
        console.log('Gemini API key not found, using sample responses');
        return this.getSampleResponse(message, context);
      }

      const model = this.getModel();

      // Create context-aware prompt
      const prompt = `You are an AI eco-assistant for GreenMate, an eco-friendly lifestyle app.

User Context:
- Level: ${context.userLevel || 1}
- Total Points: ${context.userPoints || 0}
- Current Habits: ${context.userHabits?.length || 0} habits

User Message: "${message}"

Please provide helpful, actionable advice about sustainable living. Keep responses concise (under 200 words) and include:
1. Direct answer to their question
2. 2-3 specific actionable tips
3. Encouragement related to their current level

Focus on practical, beginner-friendly advice for eco-friendly habits, waste reduction, energy saving, sustainable transportation, and green living.`;

      const result = await model.generateContent(prompt);
      const response = result.response;
      const text = response.text();

      // Generate follow-up suggestions based on the topic
      const suggestions = this.generateSuggestions(message);
      const actionItems = this.extractActionItems(text);

      return {
        response: text,
        suggestions,
        actionItems
      };

    } catch (error) {
      console.error('Gemini API Error:', error);
      // Fall back to sample responses if API fails
      return this.getSampleResponse(message, context);
    }
  }

  generateSuggestions(message) {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('habit')) {
      return [
        'How do I track my progress?',
        'What are the easiest habits to start with?',
        'How many habits should I do per day?'
      ];
    }

    if (lowerMessage.includes('waste')) {
      return [
        'How do I start composting?',
        'What can I recycle in my area?',
        'Zero waste tips for beginners'
      ];
    }

    if (lowerMessage.includes('energy')) {
      return [
        'How much can I save on my energy bill?',
        'Best energy-efficient appliances?',
        'Solar panel options for apartments'
      ];
    }

    if (lowerMessage.includes('transport')) {
      return [
        'Public transportation tips',
        'Electric vehicle benefits',
        'Bike commuting safety'
      ];
    }

    if (lowerMessage.includes('food')) {
      return [
        'Plant-based meal ideas',
        'Local farmers market guide',
        'Reducing food waste tips'
      ];
    }

    return [
      'Tell me about carbon footprint',
      'Sustainable shopping tips',
      'Green home improvements'
    ];
  }

  extractActionItems(text) {
    const actionItems = [];
    const lines = text.split('\n');

    lines.forEach(line => {
      // Look for bullet points or numbered lists
      if (line.match(/^[\s]*[•\-\*\d\.]/)) {
        const cleanLine = line.replace(/^[\s]*[•\-\*\d\.\)]\s*/, '').trim();
        if (cleanLine.length > 10 && cleanLine.length < 100) {
          actionItems.push(cleanLine);
        }
      }
    });

    // If no action items found, generate some based on common patterns
    if (actionItems.length === 0) {
      if (text.toLowerCase().includes('start')) {
        actionItems.push('Start with small, manageable changes');
      }
      if (text.toLowerCase().includes('track')) {
        actionItems.push('Track your progress daily');
      }
      if (text.toLowerCase().includes('reduce')) {
        actionItems.push('Focus on reducing one area at a time');
      }
    }

    return actionItems.slice(0, 4); // Limit to 4 action items
  }

  getSampleResponse(message, context) {
    const responses = {
      habits: [
        "Here are some beginner-friendly eco habits:\n\n• Use a reusable water bottle\n• Turn off lights when leaving rooms\n• Take shorter showers\n• Use public transportation once a week\n• Bring reusable bags when shopping",
        "Based on your current level, I recommend starting with these habits:\n\n• Switch to LED bulbs\n• Unplug electronics when not in use\n• Start composting food scraps\n• Use a programmable thermostat\n• Choose local and seasonal foods"
      ],
      waste: [
        "Great question! Here are effective ways to reduce waste at home:\n\n• Buy only what you need\n• Repurpose containers and jars\n• Donate items instead of throwing away\n• Use both sides of paper\n• Choose products with minimal packaging",
        "Waste reduction tips for your home:\n\n• Set up a recycling station\n• Use cloth napkins instead of paper\n• Repair items instead of replacing\n• Buy in bulk to reduce packaging\n• Start a compost bin for organic waste"
      ],
      energy: [
        "Energy saving tips for your apartment:\n\n• Use natural light during the day\n• Set thermostat 2-3 degrees lower in winter\n• Use cold water for washing clothes\n• Air dry clothes instead of using dryer\n• Seal gaps around windows and doors",
        "Here are apartment-friendly energy tips:\n\n• Use power strips to eliminate phantom loads\n• Replace old appliances with energy-efficient ones\n• Use ceiling fans to circulate air\n• Close curtains during hot days\n• Cook multiple items at once in the oven"
      ]
    };

    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('habit') || lowerMessage.includes('beginner')) {
      return {
        response: responses.habits[Math.floor(Math.random() * responses.habits.length)],
        suggestions: this.generateSuggestions(message),
        actionItems: [
          'Choose 1-2 habits to start with',
          'Set daily reminders',
          'Track progress for one week'
        ]
      };
    }

    if (lowerMessage.includes('waste') || lowerMessage.includes('reduce')) {
      return {
        response: responses.waste[Math.floor(Math.random() * responses.waste.length)],
        suggestions: this.generateSuggestions(message),
        actionItems: [
          'Audit your current waste',
          'Set up recycling bins',
          'Research local recycling programs'
        ]
      };
    }

    if (lowerMessage.includes('energy') || lowerMessage.includes('electric')) {
      return {
        response: responses.energy[Math.floor(Math.random() * responses.energy.length)],
        suggestions: this.generateSuggestions(message),
        actionItems: [
          'Conduct an energy audit',
          'Replace incandescent bulbs',
          'Adjust thermostat settings'
        ]
      };
    }

    // Default response
    return {
      response: "I'm here to help you live more sustainably! I can provide advice on:\n\n• Eco-friendly habits\n• Waste reduction\n• Energy saving\n• Sustainable transportation\n• Green food choices\n• Home sustainability\n\nWhat would you like to know more about?",
      suggestions: this.generateSuggestions(message),
      actionItems: [
        'Choose a sustainability topic to focus on',
        'Start with small, manageable changes',
        'Track your progress daily'
      ]
    };
  }

  async getPersonalizedSuggestions(userContext) {
    const suggestions = [
      {
        icon: '🌱',
        title: 'Start Composting',
        description: 'Turn your food scraps into nutrient-rich soil for plants'
      },
      {
        icon: '💡',
        title: 'Switch to LED Bulbs',
        description: 'Save energy and money with efficient lighting'
      },
      {
        icon: '🚌',
        title: 'Try Public Transport',
        description: 'Reduce your carbon footprint with shared transportation'
      },
      {
        icon: '♻️',
        title: 'Reduce Plastic Use',
        description: 'Choose reusable alternatives to single-use plastics'
      }
    ];

    return suggestions;
  }
}

module.exports = new GeminiService();
